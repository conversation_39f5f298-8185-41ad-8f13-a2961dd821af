"""
Enhanced Progress Overlay
========================

A robust, thread-safe progress overlay with smooth animations and proper error handling.
Unified version that replaces all previous variants.
"""

import flet as ft
import asyncio
from typing import Optional, Dict, Any, Callable
import time
import threading
import logging

from components.ui.enhanced_ui_animations_fixed import (
    AnimatedProgressBar, PulsingIcon, LoadingDots, AnimationController
)

class EnhancedProgressOverlay:
    """Enhanced progress overlay with thread-safe updates and smooth animations."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.logger = logging.getLogger(__name__)
        self.start_time = None
        self.progress_value = 0
        self.is_visible = False
        self._lock = threading.RLock()
        
        # Animation controller
        self.animation_controller = AnimationController()
        
        # Animated progress bar
        self.progress_bar = AnimatedProgressBar(
            width=400,
            height=8,
            color=ft.Colors.BLUE,
            bgcolor=ft.Colors.GREY_300
        )

        # Pulsing icon
        self.pulsing_icon = PulsingIcon(
            icon=ft.Icons.ANALYTICS,
            size=40,
            color=ft.Colors.BLUE
        )

        # Loading dots for sub-tasks
        self.loading_dots = LoadingDots(size=6, color=ft.Colors.BLUE)
        
        # Progress text
        self.progress_text = ft.Text(
            "Starting...",
            size=14,
            weight=ft.FontWeight.W_500,
            text_align=ft.TextAlign.CENTER,
            animate_opacity=300
        )
        
        # Percentage text with animation
        self.percentage_text = ft.Text(
            "0%",
            size=24,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE,
            animate_scale=300
        )
        
        # Time estimation text
        self.time_text = ft.Text(
            "Calculating time remaining...",
            size=12,
            color=ft.Colors.GREY_700,
            text_align=ft.TextAlign.CENTER
        )
        
        # Step indicator with progress dots
        self.step_container = ft.Container(
            content=ft.Column([
                ft.Text(
                    "",
                    size=11,
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Container(height=4),
                self.loading_dots.container
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            visible=False
        )
        self.step_text = self.step_container.content.controls[0]
        
        # Success checkmark (hidden initially)
        self.success_icon = ft.Icon(
            ft.Icons.CHECK_CIRCLE,
            size=60,
            color=ft.Colors.GREEN,
            visible=False,
            animate_scale=300,
            animate_opacity=300
        )
        
        # Cancel button
        self.cancel_button = ft.TextButton(
            "Cancel",
            on_click=self._on_cancel,
            visible=False,
            style=ft.ButtonStyle(
                color=ft.Colors.RED_400,
                overlay_color=ft.Colors.with_opacity(0.1, ft.Colors.RED)
            )
        )
        
        # Create the overlay content with modern styling
        self.card_content = ft.Container(
            content=ft.Column([
                ft.Stack([
                    self.pulsing_icon.container,
                    self.success_icon
                ], alignment=ft.alignment.center),
                ft.Container(height=10),
                ft.Text("Generating Comprehensive Report", 
                       size=18, 
                       weight=ft.FontWeight.BOLD,
                       text_align=ft.TextAlign.CENTER),
                ft.Container(height=20),
                self.percentage_text,
                ft.Container(height=10),
                self.progress_bar.glow_container,
                ft.Container(height=10),
                self.progress_text,
                self.time_text,
                self.step_container,
                ft.Container(height=20),
                self.cancel_button
            ], 
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=5),
            padding=30
        )
        
        self.content = ft.Container(
            content=ft.Card(
                content=self.card_content,
                elevation=8
            ),
            width=500,
            alignment=ft.alignment.center,
            visible=False,
            animate_opacity=300
        )
        
        # Backdrop with animation
        self.backdrop = ft.Container(
            content=None,
            bgcolor=ft.Colors.with_opacity(0.7, ft.Colors.BLACK),
            expand=True,
            on_click=lambda _: None,  # Prevent clicks from closing
            visible=False,
            animate_opacity=300
        )
        
        # Main container
        self.overlay = ft.Stack([
            self.backdrop,
            ft.Container(
                content=self.content,
                expand=True,
                alignment=ft.alignment.center
            )
        ])
        
        # Track if overlay has been added to page
        self._added_to_page = False
        
        # Cancellation callback
        self.on_cancel: Optional[Callable[[], None]] = None
    
    async def show(self, initial_message: str = "Starting analysis..."):
        """Show the progress overlay with animation."""
        with self._lock:
            self.is_visible = True
            self.start_time = time.time()
            self.progress_value = 0
            
            # Reset UI state
            self.progress_text.value = initial_message
            self.percentage_text.value = "0%"
            self.time_text.value = "Estimating time..."
            self.step_text.value = ""
            self.step_container.visible = False
            self.success_icon.visible = False
            
            # Reset progress bar
            try:
                await self.progress_bar.set_value(0)
            except:
                pass
            
            # Add to page overlay if needed
            if not self._added_to_page:
                self.page.overlay.append(self.overlay)
                self._added_to_page = True
                self.logger.info("Enhanced progress overlay added to page overlay")
            
            # Show with animation
            self.backdrop.visible = True
            self.backdrop.opacity = 0
            self.content.visible = True
            self.content.opacity = 0
            
            # Force initial update
            try:
                self.page.update()
            except Exception as e:
                self.logger.error(f"Error in initial page update: {e}")
            
            # Animate in
            await asyncio.sleep(0.05)
            self.backdrop.opacity = 1
            self.content.opacity = 1
            
            # Start animations
            try:
                self.page.run_task(self.pulsing_icon.start_pulsing)
                self.page.run_task(self.loading_dots.start_animation)
            except Exception as e:
                self.logger.error(f"Error starting animations: {e}")
            
            # Update page
            try:
                self.page.update()
                self.logger.info(f"Progress overlay shown: {initial_message}")
            except Exception as e:
                self.logger.error(f"Error showing progress overlay: {e}")
    
    def update_progress(self, progress: float, message: str, current_step: int = None, total_steps: int = None):
        """Thread-safe progress update method."""
        try:
            with self._lock:
                # Ensure progress overlay is visible
                if not self.is_visible:
                    self.logger.warning(f"Progress overlay not visible, auto-showing: {progress}% - {message}")
                    # Auto-show if not visible
                    self._show_sync("Starting...")
                
                # Clamp progress between 0 and 100
                progress = max(0, min(100, progress))
                self.progress_value = progress
                
                # Create update data for thread-safe execution
                update_data = {
                    'progress': progress,
                    'message': message,
                    'current_step': current_step,
                    'total_steps': total_steps
                }
                
                # Schedule UI update on main thread
                try:
                    self._schedule_ui_update(update_data)
                except Exception as e:
                    self.logger.error(f"Error scheduling UI update: {e}")
                    # Fallback to direct update
                    self._apply_update_sync(update_data)
                
        except Exception as e:
            self.logger.error(f"Error in update_progress: {e}")
    
    def _schedule_ui_update(self, update_data: Dict[str, Any]):
        """Schedule UI update on main thread using Flet's threading mechanism."""
        try:
            # Use a lambda to create a proper callable for the thread-safe update
            def ui_update_task():
                try:
                    self._apply_update_sync(update_data)
                except Exception as e:
                    self.logger.error(f"Error in scheduled UI update: {e}")
            
            # Schedule the update to run on the main thread
            # In Flet, we can use page.run_task to ensure thread-safe execution
            if hasattr(self.page, 'run_task'):
                self.page.run_task(ui_update_task)
            else:
                # Fallback to direct execution
                ui_update_task()
                
        except Exception as e:
            self.logger.error(f"Error in _schedule_ui_update: {e}")
            # Final fallback
            self._apply_update_sync(update_data)
    
    async def _apply_update_async(self, update_data: Dict[str, Any]):
        """Apply update asynchronously (called from main thread)."""
        try:
            progress = update_data.get('progress', 0)
            message = update_data.get('message', '')
            current_step = update_data.get('current_step')
            total_steps = update_data.get('total_steps')
            
            # Update progress bar with animation
            try:
                await self.progress_bar.set_value(progress / 100.0)
            except Exception as e:
                self.logger.error(f"Error updating progress bar: {e}")
            
            # Update percentage with scale animation
            old_value = int(self.progress_value)
            new_value = int(progress)
            if new_value != old_value and new_value % 10 == 0:
                try:
                    self.percentage_text.scale = 1.2
                    self.percentage_text.update()
                    await asyncio.sleep(0.1)
                    self.percentage_text.scale = 1.0
                except:
                    pass
            
            self.percentage_text.value = f"{int(progress)}%"
            self.progress_text.value = message
            
            # Update step indicator
            if current_step and total_steps:
                self.step_text.value = f"Step {current_step} of {total_steps}"
                self.step_container.visible = True
            
            # Update time estimation
            if self.start_time and progress > 0:
                elapsed_time = time.time() - self.start_time
                
                if progress >= 100:
                    self.time_text.value = f"Completed in {self._format_time(elapsed_time)}"
                else:
                    estimated_total_time = (elapsed_time / progress) * 100
                    remaining_time = max(0, estimated_total_time - elapsed_time)
                    
                    self.time_text.value = (
                        f"Elapsed: {self._format_time(elapsed_time)} | "
                        f"Remaining: ~{self._format_time(remaining_time)}"
                    )
            
            # Update UI
            try:
                self.page.update()
            except Exception as e:
                self.logger.error(f"Error updating page: {e}")
            
        except Exception as e:
            self.logger.error(f"Error applying async update: {e}")
    
    def _apply_update_sync(self, update_data: Dict[str, Any]):
        """Apply update synchronously (thread-safe method)."""
        try:
            progress = update_data.get('progress', 0)
            message = update_data.get('message', '')
            current_step = update_data.get('current_step')
            total_steps = update_data.get('total_steps')
            
            # Direct updates without animations for sync mode
            self.percentage_text.value = f"{int(progress)}%"
            self.progress_text.value = message
            
            # Update progress bar safely
            if hasattr(self.progress_bar, 'progress_bar'):
                self.progress_bar.progress_bar.value = progress / 100.0
            
            # Update step indicator
            if current_step and total_steps:
                self.step_text.value = f"Step {current_step} of {total_steps}"
                self.step_container.visible = True
            
            # Update time estimation
            if self.start_time and progress > 0:
                elapsed_time = time.time() - self.start_time
                
                if progress >= 100:
                    self.time_text.value = f"Completed in {self._format_time(elapsed_time)}"
                else:
                    estimated_total_time = (elapsed_time / progress) * 100
                    remaining_time = max(0, estimated_total_time - elapsed_time)
                    
                    self.time_text.value = (
                        f"Elapsed: {self._format_time(elapsed_time)} | "
                        f"Remaining: ~{self._format_time(remaining_time)}"
                    )
            
            # Update UI with error handling
            try:
                self.page.update()
                self.logger.info(f"Thread-safe progress UI updated: {int(progress)}% - {message}")
            except Exception as e:
                self.logger.error(f"Error in thread-safe page update: {e}")
            
        except Exception as e:
            self.logger.error(f"Error applying thread-safe update: {e}")
    
    def _show_sync(self, initial_message: str):
        """Synchronous version of show for immediate display."""
        try:
            self.is_visible = True
            self.start_time = time.time()
            self.progress_value = 0
            
            # Reset UI state
            self.progress_text.value = initial_message
            self.percentage_text.value = "0%"
            self.time_text.value = "Estimating time..."
            self.step_text.value = ""
            self.step_container.visible = False
            self.success_icon.visible = False
            
            # Add to page overlay if needed
            if not self._added_to_page:
                self.page.overlay.append(self.overlay)
                self._added_to_page = True
            
            # Show immediately
            self.backdrop.visible = True
            self.content.visible = True
            
            try:
                self.page.update()
            except Exception as e:
                self.logger.error(f"Error in sync show: {e}")
        except Exception as e:
            self.logger.error(f"Error in _show_sync: {e}")
    
    async def hide(self, final_message: str = None):
        """Hide the progress overlay with animation and cleanup.
        
        Args:
            final_message: Optional final message to display before hiding
        """
        try:
            with self._lock:
                self.is_visible = False
                
                # Stop animations first
                try:
                    self.pulsing_icon.stop_pulsing()
                    self.loading_dots.stop_animation()
                except Exception as e:
                    self.logger.error(f"Error stopping animations: {e}")
                
                # Update final message if provided
                if final_message:
                    self.progress_text.value = final_message
                    self.page.update()
                    await asyncio.sleep(0.2)
                
                # Show success animation
                try:
                    self.success_icon.visible = True
                    self.success_icon.scale = 0
                    self.success_icon.opacity = 0
                    self.page.update()
                    
                    # Animate success icon with scale and opacity
                    await asyncio.sleep(0.05)
                    self.success_icon.scale = 1.2
                    self.success_icon.opacity = 1
                    self.page.update()
                    
                    # Settle to normal size
                    await asyncio.sleep(0.1)
                    self.success_icon.scale = 1
                    self.page.update()
                    
                    # Hold success state for 1 second
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"Error in success animation: {e}")
                
                # Fade out animation
                try:
                    self.content.opacity = 0
                    self.backdrop.opacity = 0
                    self.page.update()
                    
                    await asyncio.sleep(0.3)
                except Exception as e:
                    self.logger.error(f"Error in fade out animation: {e}")
                
                # Hide elements and reset state
                self.backdrop.visible = False
                self.content.visible = False
                self.success_icon.visible = False
                self.progress_value = 0
                
                # Remove overlay from page.overlay completely when hiding
                try:
                    if self._added_to_page and self.overlay in self.page.overlay:
                        self.page.overlay.remove(self.overlay)
                        self._added_to_page = False
                        self.logger.info("Progress overlay removed from page overlay")
                    
                    self.page.update()
                    self.logger.info(f"Progress overlay hidden and removed with final message: {final_message}")
                except Exception as e:
                    self.logger.error(f"Error removing overlay: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error hiding progress overlay: {e}")
    
    def _format_time(self, seconds: float) -> str:
        """Format time in a human-readable way."""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            minutes = int(seconds / 60)
            secs = int(seconds % 60)
            return f"{minutes}m {secs}s"
        else:
            hours = int(seconds / 3600)
            minutes = int((seconds % 3600) / 60)
            return f"{hours}h {minutes}m"
    
    def _on_cancel(self, e):
        """Handle cancel button click."""
        self.logger.info("Cancel button clicked")
        if self.on_cancel:
            try:
                self.on_cancel()
            except Exception as ex:
                self.logger.error(f"Error in cancel callback: {ex}")
        else:
            self.logger.info("No cancel callback registered")
    
    def get_control(self) -> ft.Control:
        """Get the overlay control to add to the page."""
        return self.overlay
    
    def set_cancel_callback(self, callback: Callable[[], None]):
        """Set the callback for cancel button."""
        self.on_cancel = callback
        self.cancel_button.visible = True
