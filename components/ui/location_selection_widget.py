"""
Location Selection Widget
=========================

Reusable widget for location selection with preview capabilities.
"""

import flet as ft
from typing import Dict, Any, Optional, List, Callable

from models.location_config import LocationManager
from services.location_service import LocationComparisonService

class LocationSelectionWidget:
    """Reusable location selection widget with preview capabilities."""
    
    def __init__(self, page: ft.Page, compact_mode: bool = False):
        self.page = page
        self.compact_mode = compact_mode
        self.location_manager = LocationManager()
        self.location_service = LocationComparisonService()
        
        self.selected_locations: List[str] = []
        self.available_locations = self.location_manager.get_location_names()
        
        # Callbacks
        self.on_selection_changed: Optional[Callable[[List[str]], None]] = None
        
    def build(self) -> ft.Container:
        """Build the location selection widget."""
        if self.compact_mode:
            return self._build_compact_selection()
        else:
            return self._build_full_selection()
    
    def _build_compact_selection(self) -> ft.Container:
        """Build compact selection for project setup integration."""
        # Smart suggestions based on popular/recommended locations (using actual names from LocationManager)
        recommended_locations = [loc for loc in ["Ouarzazate", "Dakhla", "Laayoune_Advanced"] if loc in self.available_locations]

        # If none of the preferred names exist, use the first few available locations
        if not recommended_locations:
            recommended_locations = self.available_locations[:3]

        # Selection status with better styling
        selection_count = len(self.selected_locations)
        status_color = ft.Colors.GREEN_600 if selection_count > 0 else ft.Colors.ORANGE_600
        status_text = f"{selection_count} location{'s' if selection_count != 1 else ''} selected"

        if selection_count == 0:
            status_text = "⚠️ No locations selected"
        elif selection_count == 1:
            status_text = f"✓ {selection_count} location selected"
        else:
            status_text = f"✓ {selection_count} locations selected"

        content = ft.Column([
            # Modern header with clear status
            ft.Container(
                content=ft.Row([
                    ft.Row([
                        ft.Icon(ft.Icons.LOCATION_ON, color=ft.Colors.BLUE_600, size=24),
                        ft.Column([
                            ft.Text("Location Selection", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_800),
                            ft.Text("Choose locations for comparative analysis", size=11, color=ft.Colors.GREY_600)
                        ], spacing=2)
                    ], spacing=8),
                    ft.Container(
                        content=ft.Text(
                            status_text,
                            size=12,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=status_color,
                        padding=ft.padding.symmetric(horizontal=12, vertical=6),
                        border_radius=12
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                padding=ft.padding.only(bottom=15)
            ),

            # Quick action buttons with better styling
            ft.Row([
                ft.OutlinedButton(
                    content=ft.Row([
                        ft.Icon(ft.Icons.STAR, size=16),
                        ft.Text("Recommended", size=12)
                    ], spacing=4),
                    on_click=lambda e: self._select_recommended_locations(recommended_locations),
                    style=ft.ButtonStyle(
                        color=ft.Colors.AMBER_700,
                        bgcolor=ft.Colors.AMBER_50,
                        side=ft.BorderSide(1, ft.Colors.AMBER_300),
                        shape=ft.RoundedRectangleBorder(radius=8)
                    )
                ),
                ft.OutlinedButton(
                    content=ft.Row([
                        ft.Icon(ft.Icons.SELECT_ALL, size=16),
                        ft.Text("All", size=12)
                    ], spacing=4),
                    on_click=self._select_all_locations,
                    style=ft.ButtonStyle(
                        color=ft.Colors.BLUE_700,
                        bgcolor=ft.Colors.BLUE_50,
                        side=ft.BorderSide(1, ft.Colors.BLUE_300),
                        shape=ft.RoundedRectangleBorder(radius=8)
                    )
                ),
                ft.OutlinedButton(
                    content=ft.Row([
                        ft.Icon(ft.Icons.CLEAR, size=16),
                        ft.Text("Clear", size=12)
                    ], spacing=4),
                    on_click=self._clear_selection,
                    style=ft.ButtonStyle(
                        color=ft.Colors.GREY_700,
                        bgcolor=ft.Colors.GREY_50,
                        side=ft.BorderSide(1, ft.Colors.GREY_300),
                        shape=ft.RoundedRectangleBorder(radius=8)
                    )
                )
            ], alignment=ft.MainAxisAlignment.START, spacing=8),

            ft.Container(height=15),

            # Location grid with previews
            self._create_location_grid_with_previews()
        ])
        
        return ft.Container(
            content=content,
            padding=15,
            bgcolor=ft.Colors.GREEN_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREEN_200)
        )
    
    def _build_full_selection(self) -> ft.Container:
        """Build full selection for dedicated location comparison view."""
        # Create checkboxes for all locations
        location_checkboxes = []
        for location in self.available_locations:
            checkbox = ft.Checkbox(
                label=location,
                value=location in self.selected_locations,
                on_change=lambda e, loc=location: self._on_location_toggle(loc, e.control.value)
            )
            location_checkboxes.append(checkbox)
        
        # Organize in rows of 3
        checkbox_rows = []
        for i in range(0, len(location_checkboxes), 3):
            row_checkboxes = location_checkboxes[i:i+3]
            checkbox_rows.append(ft.Row(row_checkboxes))
        
        content = ft.Column([
            ft.Text("Select Locations to Compare:", size=16, weight=ft.FontWeight.BOLD),
            ft.Text(f"Currently selected: {len(self.selected_locations)} locations", 
                   size=12, color=ft.Colors.GREY_600),
            ft.Divider(height=10),
            *checkbox_rows,
            ft.Divider(height=20),
            ft.Row([
                ft.ElevatedButton(
                    "Select All",
                    icon=ft.Icons.SELECT_ALL,
                    on_click=self._select_all_locations,
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE
                ),
                ft.ElevatedButton(
                    "Clear All",
                    icon=ft.Icons.CLEAR_ALL,
                    on_click=self._clear_selection,
                    bgcolor=ft.Colors.GREY_600,
                    color=ft.Colors.WHITE
                )
            ], alignment=ft.MainAxisAlignment.CENTER)
        ])
        
        return ft.Container(content=content, padding=10)
    
    def _create_location_grid_with_previews(self) -> ft.Container:
        """Create location grid with preview metrics."""
        location_cards = []
        
        # Debug: Print available locations to see what we actually have
        print(f"DEBUG: Available locations: {self.available_locations}")
        
        # Group locations by region using ACTUAL location names from LocationManager
        location_groups = {
            "Morocco Premium": ["Ouarzazate", "Dakhla", "Laayoune_Advanced"],
            "Morocco Extended": ["Noor_Midelt", "Laâyoune"], 
            "Europe": ["Sicily_Catania", "Puglia_Brindisi", "Andalusia_Sevilla"]
        }
        
        # Update groups to only include locations that actually exist
        final_groups = {}
        for group_name, group_locations in location_groups.items():
            available_in_group = []
            for loc in group_locations:
                # Check for exact match or close match in available locations
                if loc in self.available_locations:
                    available_in_group.append(loc)
                else:
                    # Try to find similar named locations
                    for available_loc in self.available_locations:
                        if (loc.replace("_", " ").lower() == available_loc.replace("_", " ").lower() or
                            loc.lower() == available_loc.lower()):
                            available_in_group.append(available_loc)
                            break
            
            if available_in_group:
                final_groups[group_name] = available_in_group
        
        location_groups = final_groups
        print(f"DEBUG: Final location groups: {location_groups}")
        
        for group_name, locations in location_groups.items():
            # Group header
            if any(loc in self.available_locations for loc in locations):
                location_cards.append(
                    ft.Container(
                        content=ft.Text(
                            group_name,
                            size=12,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_700
                        ),
                        padding=ft.padding.only(top=10, bottom=5)
                    )
                )
            
            # Location cards in this group
            group_cards = []
            for location in locations:
                if location in self.available_locations:
                    card = self._create_location_preview_card(location)
                    group_cards.append(card)
            
            # Arrange group cards in rows of 2
            for i in range(0, len(group_cards), 2):
                row_cards = group_cards[i:i+2]
                location_cards.append(
                    ft.Row(row_cards, alignment=ft.MainAxisAlignment.START, spacing=8)
                )
        
        return ft.Container(
            content=ft.Column(location_cards, spacing=5),
            height=300,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300),
            padding=10
        )
    
    def _create_location_preview_card(self, location_name: str) -> ft.Container:
        """Create a preview card for a location."""
        location_config = self.location_manager.get_location(location_name)
        if not location_config:
            return ft.Container()

        is_selected = location_name in self.selected_locations

        # Calculate preview metrics
        estimated_lcoe = location_config.get_lcoe_estimate()
        capacity_factor = location_config.calculate_capacity_factor(10.0)  # Assume 10MW for preview

        # Format display values
        lcoe_display = f"€{estimated_lcoe:.3f}/kWh" if estimated_lcoe != float('inf') else "N/A"
        cf_display = f"{capacity_factor:.1%}"
        irradiation_display = f"{location_config.irradiation_kwh_m2:.0f} kWh/m²"

        # Clean location name for display
        display_name = location_name.replace('_', ' ').replace('Advanced', '').strip()

        # Create modern card design
        card_content = ft.Column([
            # Header with selection state
            ft.Row([
                ft.Container(
                    content=ft.Icon(
                        ft.Icons.CHECK_CIRCLE if is_selected else ft.Icons.LOCATION_ON,
                        color=ft.Colors.WHITE if is_selected else ft.Colors.BLUE_600,
                        size=20
                    ),
                    bgcolor=ft.Colors.GREEN_600 if is_selected else ft.Colors.TRANSPARENT,
                    border_radius=10,
                    padding=4
                ),
                ft.Column([
                    ft.Text(
                        display_name,
                        size=13,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.WHITE if is_selected else ft.Colors.BLUE_800
                    ),
                    ft.Text(
                        "Selected" if is_selected else "Available",
                        size=9,
                        color=ft.Colors.WHITE if is_selected else ft.Colors.GREY_600
                    )
                ], spacing=1, expand=True)
            ], spacing=8),

            ft.Container(height=8),

            # Metrics with improved layout
            ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.WB_SUNNY, size=16, color=ft.Colors.ORANGE_600),
                    ft.Text(irradiation_display, size=11, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.WHITE if is_selected else ft.Colors.GREY_800)
                ], spacing=6),
                ft.Row([
                    ft.Icon(ft.Icons.BATTERY_CHARGING_FULL, size=16, color=ft.Colors.GREEN_600),
                    ft.Text(cf_display, size=11, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.WHITE if is_selected else ft.Colors.GREY_800)
                ], spacing=6),
                ft.Row([
                    ft.Icon(ft.Icons.EURO, size=16, color=ft.Colors.BLUE_600),
                    ft.Text(lcoe_display, size=11, weight=ft.FontWeight.BOLD,
                           color=ft.Colors.WHITE if is_selected else ft.Colors.GREY_800)
                ], spacing=6)
            ], spacing=4)
        ], spacing=0)

        # Different styling for selected vs unselected
        if is_selected:
            card_style = {
                'bgcolor': ft.Colors.BLUE_600,
                'border': ft.border.all(2, ft.Colors.BLUE_800),
                'shadow': ft.BoxShadow(
                    spread_radius=1,
                    blur_radius=8,
                    color=ft.Colors.with_opacity(0.3, ft.Colors.BLUE_600),
                    offset=ft.Offset(0, 2)
                )
            }
        else:
            card_style = {
                'bgcolor': ft.Colors.WHITE,
                'border': ft.border.all(1, ft.Colors.GREY_300),
                'shadow': ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=4,
                    color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                    offset=ft.Offset(0, 2)
                )
            }

        return ft.Container(
            content=card_content,
            width=170,
            height=130,
            padding=12,
            border_radius=12,
            on_click=lambda e, loc=location_name: self._on_card_click(loc),
            **card_style
        )
    
    def _on_location_toggle(self, location: str, selected: bool):
        """Handle location toggle."""
        if selected and location not in self.selected_locations:
            self.selected_locations.append(location)
        elif not selected and location in self.selected_locations:
            self.selected_locations.remove(location)
        
        self._notify_selection_changed()
        self._refresh_widget()
    
    def _on_card_click(self, location: str):
        """Handle location card click to toggle selection."""
        is_selected = location in self.selected_locations
        self._on_location_toggle(location, not is_selected)
    
    def _select_recommended_locations(self, recommended: List[str]):
        """Select recommended locations."""
        self.selected_locations = [loc for loc in recommended if loc in self.available_locations]
        self._notify_selection_changed()
        self._refresh_widget()
    
    def _select_all_locations(self, e=None):
        """Select all available locations."""
        self.selected_locations = self.available_locations.copy()
        self._notify_selection_changed()
        self._refresh_widget()
    
    def _clear_selection(self, e=None):
        """Clear all selections."""
        self.selected_locations = []
        self._notify_selection_changed()
        self._refresh_widget()
    
    def _notify_selection_changed(self):
        """Notify about selection changes."""
        if self.on_selection_changed:
            self.on_selection_changed(self.selected_locations.copy())
    
    def _refresh_widget(self):
        """Refresh the widget display."""
        if self.page:
            self.page.update()
    
    def get_selected_locations(self) -> List[str]:
        """Get currently selected locations."""
        return self.selected_locations.copy()
    
    def set_selected_locations(self, locations: List[str]):
        """Set selected locations."""
        self.selected_locations = [loc for loc in locations if loc in self.available_locations]
        self._refresh_widget()
    
    def set_selection_callback(self, callback: Callable[[List[str]], None]):
        """Set the selection changed callback."""
        self.on_selection_changed = callback
    
    def get_selection_summary(self) -> Dict[str, Any]:
        """Get summary of current selection."""
        if not self.selected_locations:
            return {
                "count": 0,
                "regions": [],
                "estimated_analysis_time": "N/A"
            }
        
        # Analyze selected locations
        regions = set()
        for location in self.selected_locations:
            if any(morocco_loc in location for morocco_loc in ["Ouarzazate", "Dakhla", "Laayoune", "Noor"]):
                regions.add("Morocco")
            elif any(italy_loc in location for italy_loc in ["Sicily", "Puglia"]):
                regions.add("Italy")
            elif "Andalusia" in location:
                regions.add("Spain")
        
        # Estimate analysis time (rough calculation)
        base_time = 30  # seconds per location
        estimated_seconds = len(self.selected_locations) * base_time
        estimated_time = f"{estimated_seconds // 60}m {estimated_seconds % 60}s" if estimated_seconds >= 60 else f"{estimated_seconds}s"
        
        return {
            "count": len(self.selected_locations),
            "regions": list(regions),
            "estimated_analysis_time": estimated_time,
            "has_comparison": len(self.selected_locations) > 1
        }
