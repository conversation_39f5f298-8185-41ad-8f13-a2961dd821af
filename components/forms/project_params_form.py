"""
Project Parameters Form
=======================

Form component for project parameters data entry.
"""

import flet as ft
from typing import Optional, Callable, Any, Dict, Union

from models.project_assumptions import EnhancedProjectAssumptions


class ProjectParamsForm:
    """Form component for project parameters."""
    
    def __init__(self, project_assumptions: EnhancedProjectAssumptions):
        self.project_assumptions = project_assumptions
        self.on_data_changed: Optional[Callable[[str, Any], None]] = None
        
        # Form fields - will be created in build()
        self.fields: Dict[str, Union[ft.TextField, ft.Dropdown]] = {}
    
    def build(self) -> ft.Container:
        """Build the project parameters form."""
        
        # Technical parameters section
        technical_section = self._create_technical_section()
        
        # Financial parameters section
        financial_section = self._create_financial_section()
        
        # Grant parameters section
        grant_section = self._create_grant_section()
        
        return ft.Container(
            content=ft.Column([
                technical_section,
                ft.Container(height=20),
                financial_section,
                ft.Container(height=20),
                grant_section
            ]),
            padding=15
        )
    
    def _create_technical_section(self) -> ft.Container:
        """Create technical parameters section with modern styling."""
        return ft.Container(
            content=ft.Column([
                # Modern section header
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Icon(ft.Icons.ENGINEERING, color=ft.Colors.WHITE, size=20),
                            width=40,
                            height=40,
                            bgcolor=ft.Colors.BLUE_600,
                            border_radius=20,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(width=12),
                        ft.Text("Technical Parameters", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                    ]),
                    padding=ft.padding.only(bottom=20),
                ),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("capacity_mw", "Capacity", "10.0", "MW", ft.Icons.BOLT, "Total installed capacity of the project")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("project_life_years", "Project Life", "25", "years", ft.Icons.SCHEDULE, "Expected operational lifetime of the project")
                    ], col={"sm": 6})
                ]),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("production_mwh_year1", "Production Year 1", "18000", "MWh", ft.Icons.ENERGY_SAVINGS_LEAF, "Expected energy production in first year")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("degradation_rate", "Degradation Rate", "0.5", "%/year", ft.Icons.TRENDING_DOWN, "Annual degradation rate as percentage")
                    ], col={"sm": 6})
                ]),

                # Add project location field
                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_location_field("project_location", "Project Location", "Ouarzazate", "Select the primary location for your project")
                    ], col={"sm": 12})
                ])
            ]),
            padding=25,
            bgcolor=ft.Colors.BLUE_50,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.BLUE_100)
        )
    
    def _create_financial_section(self) -> ft.Container:
        """Create financial parameters section with modern styling."""
        return ft.Container(
            content=ft.Column([
                # Modern section header
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Icon(ft.Icons.EURO_SYMBOL, color=ft.Colors.WHITE, size=20),
                            width=40,
                            height=40,
                            bgcolor=ft.Colors.GREEN_600,
                            border_radius=20,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(width=12),
                        ft.Text("Financial Parameters", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
                    ]),
                    padding=ft.padding.only(bottom=20),
                ),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("ppa_price_eur_kwh", "PPA Price", "0.045", "€/kWh",
                                                ft.Icons.HANDSHAKE, "Power Purchase Agreement price in euros per kWh")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("ppa_escalation", "PPA Escalation", "0.0", "%",
                                                ft.Icons.TRENDING_UP, "Annual escalation rate of PPA price as percentage")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("debt_ratio", "Debt Ratio", "0.75", "ratio",
                                                ft.Icons.PIE_CHART, "Debt-to-total-financing ratio (0-1)")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("interest_rate", "Interest Rate", "6.0", "%",
                                                ft.Icons.PERCENT, "Annual interest rate on debt as percentage")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("debt_years", "Debt Tenor", "15", "years",
                                                ft.Icons.SCHEDULE, "Duration of debt repayment period")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("discount_rate", "Discount Rate", "8.0", "%",
                                                ft.Icons.CALCULATE, "Discount rate for NPV calculations as percentage")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("tax_rate", "Tax Rate", "15.0", "%",
                                                ft.Icons.ACCOUNT_BALANCE, "Corporate tax rate as percentage")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("land_lease_eur_mw_year", "Land Lease", "2000", "€/MW/year",
                                                ft.Icons.LANDSCAPE, "Annual land lease cost per MW in euros")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("tax_holiday", "Tax Grace Period", "5", "years",
                                                ft.Icons.HOLIDAY_VILLAGE, "Number of years with tax exemption")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("grace_years", "Debt Grace Period", "2", "years",
                                                ft.Icons.PAUSE_CIRCLE, "Number of years before debt principal repayment starts")
                    ], col={"sm": 6})
                ])
            ]),
            padding=25,
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_grant_section(self) -> ft.Container:
        """Create grant parameters section with modern styling."""
        return ft.Container(
            content=ft.Column([
                # Modern section header
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Icon(ft.Icons.CARD_GIFTCARD, color=ft.Colors.WHITE, size=20),
                            width=40,
                            height=40,
                            bgcolor=ft.Colors.PURPLE_600,
                            border_radius=20,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(width=12),
                        ft.Text("Grant Parameters", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_700),
                    ]),
                    padding=ft.padding.only(bottom=20),
                ),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("grant_meur_italy", "Italian Grant", "0.0", "M€",
                                                ft.Icons.FLAG, "Grant funding from Italian sources in millions of euros")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("grant_meur_masen", "MASEN Grant", "0.0", "M€",
                                                ft.Icons.SOLAR_POWER, "Grant funding from MASEN in millions of euros")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("grant_meur_connection", "Connection Grant", "0.0", "M€",
                                                ft.Icons.ELECTRICAL_SERVICES, "Grant funding for grid connection in millions of euros")
                    ], col={"sm": 6}),
                    ft.Column([
                        self._create_modern_field("grant_meur_simest_africa", "SIMEST African Fund", "0.0", "M€",
                                                ft.Icons.PUBLIC, "Grant funding from SIMEST African Fund in millions of euros")
                    ], col={"sm": 6})
                ]),

                ft.Container(height=15),

                ft.ResponsiveRow([
                    ft.Column([
                        self._create_modern_field("grant_meur_cri", "CRI Regional Investment Grant", "0.0", "M€",
                                                ft.Icons.LOCATION_CITY, "Grant funding from CRI Regional Investment Center in millions of euros")
                    ], col={"sm": 12})
                ])
            ]),
            padding=25,
            bgcolor=ft.Colors.WHITE,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_field(self, field_name: str, label: str, default_value: str, tooltip: str = None) -> ft.TextField:
        """Create a form field."""
        current_value = getattr(self.project_assumptions, field_name, default_value)

        # Handle percentage display for percentage fields
        if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
            display_value = float(current_value) * 100.0 if current_value else 0.0
        else:
            display_value = current_value

        field = ft.TextField(
            label=label,
            value=str(display_value),
            hint_text=f"Enter {label.lower()}",
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            expand=True,
            tooltip=tooltip
        )

        self.fields[field_name] = field
        return field

    def _create_modern_field(self, field_name: str, label: str, default_value: str,
                            unit: str, icon: str, tooltip: str = None) -> ft.Container:
        """Create a modern styled form field."""
        current_value = getattr(self.project_assumptions, field_name, default_value)

        # Handle percentage display for percentage fields
        if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
            display_value = float(current_value) * 100.0 if current_value else 0.0
        else:
            display_value = current_value

        field = ft.TextField(
            label=label,
            value=str(display_value),
            hint_text=f"Enter {label.lower()}",
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            prefix_icon=icon,
            suffix_text=unit,
            expand=True,
            tooltip=tooltip,
            # Modern styling
            border_radius=10,
            filled=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            focused_border_color=ft.Colors.BLUE_500,
            focused_bgcolor=ft.Colors.BLUE_50,
            content_padding=ft.padding.symmetric(horizontal=15, vertical=12)
        )

        self.fields[field_name] = field
        return ft.Container(
            content=field,
            margin=ft.margin.only(bottom=15)
        )
    
    def _create_location_field(self, field_name: str, label: str, default_value: str, tooltip: str = None) -> ft.Dropdown:
        """Create a location dropdown field with specific cities instead of countries."""
        current_value = getattr(self.project_assumptions, field_name, default_value)

        # Use specific cities instead of generic country names
        dropdown = ft.Dropdown(
            label=label,
            value=current_value,
            options=[
                # Morocco locations
                ft.dropdown.Option("Ouarzazate"),
                ft.dropdown.Option("Dakhla"),
                ft.dropdown.Option("Laâyoune"),
                ft.dropdown.Option("Noor_Midelt"),
                ft.dropdown.Option("Tarfaya"),
                # Italy locations
                ft.dropdown.Option("Sicily_Catania"),
                ft.dropdown.Option("Puglia_Brindisi"),
                # Spain locations
                ft.dropdown.Option("Andalusia_Sevilla"),
                # Other countries (for backward compatibility)
                ft.dropdown.Option("France"),
                ft.dropdown.Option("Tunisia"),
                ft.dropdown.Option("Algeria"),
                ft.dropdown.Option("Other")
            ],
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            expand=True,
            tooltip=tooltip
        )

        self.fields[field_name] = dropdown
        return dropdown

    def _create_modern_location_field(self, field_name: str, label: str, default_value: str, tooltip: str = None) -> ft.Container:
        """Create a modern styled location dropdown field."""
        current_value = getattr(self.project_assumptions, field_name, default_value)

        dropdown = ft.Dropdown(
            label=label,
            value=current_value,
            options=[
                # Morocco locations (primary focus)
                ft.dropdown.Option("Ouarzazate", text="🇲🇦 Ouarzazate"),
                ft.dropdown.Option("Dakhla", text="🇲🇦 Dakhla"),
                ft.dropdown.Option("Laâyoune", text="🇲🇦 Laâyoune"),
                ft.dropdown.Option("Noor_Midelt", text="🇲🇦 Noor Midelt"),
                ft.dropdown.Option("Tarfaya", text="🇲🇦 Tarfaya"),
                # Italy locations
                ft.dropdown.Option("Sicily_Catania", text="🇮🇹 Sicily (Catania)"),
                ft.dropdown.Option("Puglia_Brindisi", text="🇮🇹 Puglia (Brindisi)"),
                # Spain locations
                ft.dropdown.Option("Andalusia_Sevilla", text="🇪🇸 Andalusia (Sevilla)"),
                # Other countries (for backward compatibility)
                ft.dropdown.Option("France", text="🇫🇷 France"),
                ft.dropdown.Option("Tunisia", text="🇹🇳 Tunisia"),
                ft.dropdown.Option("Algeria", text="🇩🇿 Algeria"),
                ft.dropdown.Option("Other", text="🌍 Other")
            ],
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            expand=True,
            tooltip=tooltip,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.BLUE_200,
            border_radius=8,
            content_padding=ft.padding.all(12)
        )

        self.fields[field_name] = dropdown

        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.LOCATION_ON, color=ft.Colors.BLUE_600, size=20),
                    ft.Text(label, size=14, weight=ft.FontWeight.W_500, color=ft.Colors.BLUE_700),
                ], spacing=8),
                ft.Container(height=8),
                dropdown,
                ft.Container(
                    content=ft.Text(
                        tooltip or "Select the primary location for your project",
                        size=11,
                        color=ft.Colors.GREY_600,
                        italic=True
                    ),
                    margin=ft.margin.only(top=4)
                ) if tooltip else ft.Container()
            ]),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.BLUE_100),
            margin=ft.margin.only(bottom=15)
        )
    
    def _on_field_changed(self, field_name: str, value: str):
        """Handle field value changes."""
        try:
            # Convert to appropriate type
            if field_name in ['capacity_mw', 'production_mwh_year1', 'capex_meur', 'opex_keuros_year1',
                             'ppa_price_eur_kwh', 'debt_ratio', 'interest_rate', 'discount_rate',
                             'tax_rate', 'degradation_rate', 'ppa_escalation', 'land_lease_eur_mw_year',
                             'grant_meur_italy', 'grant_meur_masen', 'grant_meur_connection', 'grant_meur_simest_africa',
                             'grant_meur_cri']:
                numeric_value = float(value) if value else 0.0
                
                # Convert percentages to decimals
                if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
                    numeric_value = numeric_value / 100.0
                
                if self.on_data_changed:
                    self.on_data_changed(field_name, numeric_value)
            
            elif field_name in ['project_life_years', 'debt_years', 'tax_holiday', 'grace_years']:
                int_value = int(float(value)) if value else 0
                if self.on_data_changed:
                    self.on_data_changed(field_name, int_value)
            
            else:
                if self.on_data_changed:
                    self.on_data_changed(field_name, value)
        
        except ValueError:
            # Invalid number, ignore
            pass
    
    def update_data(self, project_assumptions: EnhancedProjectAssumptions):
        """Update form with new project assumptions data."""
        self.project_assumptions = project_assumptions

        # Update all fields
        for field_name, field in self.fields.items():
            value = getattr(project_assumptions, field_name, 0)

            # Convert decimals to percentages for display
            if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
                value = value * 100.0

            field.value = str(value)

        # Trigger UI update for all fields
        for field in self.fields.values():
            field.update()
    
    def validate(self) -> Dict[str, str]:
        """Validate form data."""
        return self.project_assumptions.validate_all()
    
    def get_data(self) -> EnhancedProjectAssumptions:
        """Get current form data as EnhancedProjectAssumptions."""
        return self.project_assumptions
