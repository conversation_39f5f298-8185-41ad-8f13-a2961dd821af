"""
Advanced 3D Charts
==================

Interactive 3D visualizations for financial analysis using Plotly.
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import json

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False


class Advanced3DChartsService:
    """Service for creating advanced 3D interactive charts."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.default_layout = {
            'font': {'family': 'Arial, sans-serif', 'size': 12},
            'plot_bgcolor': 'rgba(0,0,0,0)',
            'paper_bgcolor': 'rgba(0,0,0,0)',
            'margin': {'l': 50, 'r': 50, 't': 80, 'b': 50}
        }
        
        if not PLOTLY_AVAILABLE:
            self.logger.warning("Plotly not available - 3D charts will use fallback HTML")
    
    def create_sensitivity_surface(self, 
                                 x_param: str, x_values: List[float],
                                 y_param: str, y_values: List[float],
                                 z_values: np.ndarray,
                                 title: str = "3D Sensitivity Analysis") -> str:
        """Create 3D sensitivity surface plot."""
        if not PLOTLY_AVAILABLE:
            return self._create_fallback_html(title, "3D Sensitivity Surface")
        
        try:
            fig = go.Figure(data=[go.Surface(
                x=x_values,
                y=y_values,
                z=z_values,
                colorscale='Viridis',
                opacity=0.9,
                showscale=True,
                colorbar={'title': 'IRR (%)'}
            )])
            
            fig.update_layout(
                title={
                    'text': title,
                    'x': 0.5,
                    'font': {'size': 16, 'color': '#2E86AB'}
                },
                scene={
                    'xaxis': {
                        'title': x_param.replace('_', ' ').title(),
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True,
                        'zerolinecolor': "white"
                    },
                    'yaxis': {
                        'title': y_param.replace('_', ' ').title(),
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True,
                        'zerolinecolor': "white"
                    },
                    'zaxis': {
                        'title': 'IRR (%)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True,
                        'zerolinecolor': "white"
                    },
                    'camera': {
                        'eye': {'x': 1.87, 'y': 0.88, 'z': -0.64}
                    }
                },
                width=800,
                height=600,
                **self.default_layout
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id="sensitivity_surface")
            
        except Exception as e:
            self.logger.error(f"Error creating 3D sensitivity surface: {e}")
            return self._create_fallback_html(title, "Error creating 3D chart")
    
    def create_scenario_comparison_3d(self, scenarios_data: Dict[str, Dict[str, float]], 
                                     title: str = "3D Scenario Comparison") -> str:
        """Create 3D scatter plot for scenario comparison."""
        if not PLOTLY_AVAILABLE:
            return self._create_fallback_html(title, "3D Scenario Comparison")
        
        try:
            scenario_names = []
            irr_values = []
            npv_values = []
            lcoe_values = []
            colors = []
            
            color_map = {
                'optimistic': '#28a745',
                'base': '#007bff', 
                'pessimistic': '#dc3545',
                'best_case': '#17a2b8',
                'worst_case': '#fd7e14'
            }
            
            for scenario_name, metrics in scenarios_data.items():
                scenario_names.append(scenario_name.title())
                irr_values.append(metrics.get('irr_equity', 0) * 100)
                npv_values.append(metrics.get('npv_equity', 0) / 1000000)  # Convert to millions
                lcoe_values.append(metrics.get('lcoe', 0) * 100)  # Convert to cents
                
                # Assign colors based on scenario type
                scenario_lower = scenario_name.lower()
                if 'optimistic' in scenario_lower or 'best' in scenario_lower:
                    colors.append(color_map.get('optimistic', '#28a745'))
                elif 'pessimistic' in scenario_lower or 'worst' in scenario_lower:
                    colors.append(color_map.get('pessimistic', '#dc3545'))
                else:
                    colors.append(color_map.get('base', '#007bff'))
            
            fig = go.Figure(data=[go.Scatter3d(
                x=irr_values,
                y=npv_values,
                z=lcoe_values,
                mode='markers+text',
                text=scenario_names,
                textposition="top center",
                marker={
                    'size': 12,
                    'color': colors,
                    'opacity': 0.8,
                    'line': {'width': 2, 'color': 'DarkSlateGrey'}
                },
                hovertemplate=
                '<b>%{text}</b><br>' +
                'IRR: %{x:.1f}%<br>' +
                'NPV: €%{y:.1f}M<br>' +
                'LCOE: %{z:.1f} ¢/kWh<br>' +
                '<extra></extra>'
            )])
            
            fig.update_layout(
                title={
                    'text': title,
                    'x': 0.5,
                    'font': {'size': 16, 'color': '#2E86AB'}
                },
                scene={
                    'xaxis': {
                        'title': 'IRR (%)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'yaxis': {
                        'title': 'NPV (€ Millions)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'zaxis': {
                        'title': 'LCOE (¢/kWh)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'camera': {
                        'eye': {'x': 1.5, 'y': 1.5, 'z': 1.5}
                    }
                },
                width=800,
                height=600,
                **self.default_layout
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id="scenario_comparison_3d")
            
        except Exception as e:
            self.logger.error(f"Error creating 3D scenario comparison: {e}")
            return self._create_fallback_html(title, "Error creating 3D chart")
    
    def create_monte_carlo_distribution_3d(self, mc_results: Dict[str, Any], 
                                          title: str = "3D Monte Carlo Distribution") -> str:
        """Create 3D distribution plot for Monte Carlo results."""
        if not PLOTLY_AVAILABLE:
            return self._create_fallback_html(title, "3D Monte Carlo Distribution")
        
        try:
            # Extract results from Monte Carlo data
            results_df = pd.DataFrame(mc_results.get('results', {}))
            
            if results_df.empty:
                return self._create_fallback_html(title, "No Monte Carlo data available")
            
            # Create 3D histogram/surface
            irr_values = results_df.get('irr_equity', []) * 100
            npv_values = results_df.get('npv_equity', []) / 1000000
            lcoe_values = results_df.get('lcoe', []) * 100
            
            fig = go.Figure()
            
            # Add scatter plot for all points
            fig.add_trace(go.Scatter3d(
                x=irr_values,
                y=npv_values,
                z=lcoe_values,
                mode='markers',
                marker={
                    'size': 3,
                    'color': irr_values,
                    'colorscale': 'Viridis',
                    'opacity': 0.6,
                    'colorbar': {'title': 'IRR (%)'}
                },
                name='Monte Carlo Results',
                hovertemplate=
                'IRR: %{x:.1f}%<br>' +
                'NPV: €%{y:.1f}M<br>' +
                'LCOE: %{z:.1f} ¢/kWh<br>' +
                '<extra></extra>'
            ))
            
            # Add confidence ellipsoid (simplified as sphere for demonstration)
            if len(irr_values) > 10:
                mean_irr = np.mean(irr_values)
                mean_npv = np.mean(npv_values)
                mean_lcoe = np.mean(lcoe_values)
                
                std_irr = np.std(irr_values)
                std_npv = np.std(npv_values)
                std_lcoe = np.std(lcoe_values)
                
                # Create ellipsoid points (simplified)
                theta = np.linspace(0, 2*np.pi, 20)
                phi = np.linspace(0, np.pi, 20)
                theta, phi = np.meshgrid(theta, phi)
                
                x_ellipse = mean_irr + std_irr * np.sin(phi) * np.cos(theta)
                y_ellipse = mean_npv + std_npv * np.sin(phi) * np.sin(theta)
                z_ellipse = mean_lcoe + std_lcoe * np.cos(phi)
                
                fig.add_trace(go.Surface(
                    x=x_ellipse,
                    y=y_ellipse,
                    z=z_ellipse,
                    opacity=0.3,
                    colorscale='Blues',
                    showscale=False,
                    name='Confidence Region'
                ))
            
            fig.update_layout(
                title={
                    'text': title,
                    'x': 0.5,
                    'font': {'size': 16, 'color': '#2E86AB'}
                },
                scene={
                    'xaxis': {
                        'title': 'IRR (%)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'yaxis': {
                        'title': 'NPV (€ Millions)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'zaxis': {
                        'title': 'LCOE (¢/kWh)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'camera': {
                        'eye': {'x': 2, 'y': 2, 'z': 1}
                    }
                },
                width=800,
                height=600,
                **self.default_layout
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id="monte_carlo_3d")
            
        except Exception as e:
            self.logger.error(f"Error creating 3D Monte Carlo distribution: {e}")
            return self._create_fallback_html(title, "Error creating 3D chart")
    
    def create_parameter_optimization_surface(self, optimization_data: Dict[str, Any],
                                            title: str = "Parameter Optimization Surface") -> str:
        """Create 3D surface showing parameter optimization landscape."""
        if not PLOTLY_AVAILABLE:
            return self._create_fallback_html(title, "Parameter Optimization Surface")
        
        try:
            # Generate optimization surface data
            param1_values = optimization_data.get('param1_values', np.linspace(0.8, 1.2, 20))
            param2_values = optimization_data.get('param2_values', np.linspace(0.8, 1.2, 20))
            objective_surface = optimization_data.get('objective_surface', np.random.rand(20, 20))
            
            # Find optimal point
            optimal_idx = np.unravel_index(np.argmax(objective_surface), objective_surface.shape)
            optimal_x = param1_values[optimal_idx[1]]
            optimal_y = param2_values[optimal_idx[0]]
            optimal_z = objective_surface[optimal_idx]
            
            fig = go.Figure()
            
            # Add surface
            fig.add_trace(go.Surface(
                x=param1_values,
                y=param2_values,
                z=objective_surface,
                colorscale='RdYlBu_r',
                opacity=0.8,
                name='Objective Function',
                colorbar={'title': 'Objective Value'}
            ))
            
            # Add optimal point
            fig.add_trace(go.Scatter3d(
                x=[optimal_x],
                y=[optimal_y],
                z=[optimal_z],
                mode='markers',
                marker={
                    'size': 15,
                    'color': 'red',
                    'symbol': 'diamond'
                },
                name='Optimal Point',
                hovertemplate=
                '<b>Optimal Point</b><br>' +
                'Param 1: %{x:.3f}<br>' +
                'Param 2: %{y:.3f}<br>' +
                'Objective: %{z:.3f}<br>' +
                '<extra></extra>'
            ))
            
            fig.update_layout(
                title={
                    'text': title,
                    'x': 0.5,
                    'font': {'size': 16, 'color': '#2E86AB'}
                },
                scene={
                    'xaxis': {
                        'title': 'Parameter 1 (Multiplier)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'yaxis': {
                        'title': 'Parameter 2 (Multiplier)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'zaxis': {
                        'title': 'Objective Value',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'camera': {
                        'eye': {'x': 1.25, 'y': 1.25, 'z': 1.25}
                    }
                },
                width=800,
                height=600,
                **self.default_layout
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id="optimization_surface")
            
        except Exception as e:
            self.logger.error(f"Error creating optimization surface: {e}")
            return self._create_fallback_html(title, "Error creating 3D chart")
    
    def create_risk_analysis_3d(self, risk_data: Dict[str, Any],
                               title: str = "3D Risk Analysis") -> str:
        """Create 3D visualization for risk analysis."""
        if not PLOTLY_AVAILABLE:
            return self._create_fallback_html(title, "3D Risk Analysis")
        
        try:
            # Extract risk metrics
            probability = risk_data.get('probability', [0.1, 0.3, 0.6, 0.8, 0.9])
            impact = risk_data.get('impact', [0.2, 0.4, 0.6, 0.8, 1.0])
            mitigation_cost = risk_data.get('mitigation_cost', [100, 200, 300, 400, 500])
            risk_categories = risk_data.get('categories', ['Market', 'Technology', 'Financial', 'Regulatory', 'Operational'])
            
            # Create risk bubble chart
            fig = go.Figure(data=[go.Scatter3d(
                x=probability,
                y=impact,
                z=mitigation_cost,
                mode='markers+text',
                text=risk_categories,
                textposition="top center",
                marker={
                    'size': [20, 25, 30, 35, 40],  # Bubble sizes
                    'color': [prob * imp for prob, imp in zip(probability, impact)],  # Risk score
                    'colorscale': 'Reds',
                    'opacity': 0.7,
                    'colorbar': {'title': 'Risk Score'},
                    'line': {'width': 2, 'color': 'DarkSlateGrey'}
                },
                hovertemplate=
                '<b>%{text}</b><br>' +
                'Probability: %{x:.1%}<br>' +
                'Impact: %{y:.1%}<br>' +
                'Mitigation Cost: €%{z:,.0f}k<br>' +
                '<extra></extra>'
            )])
            
            # Add risk threshold plane
            xx, yy = np.meshgrid(np.linspace(0, 1, 10), np.linspace(0, 1, 10))
            zz = np.full_like(xx, 300)  # Threshold plane at 300k
            
            fig.add_trace(go.Surface(
                x=xx,
                y=yy,
                z=zz,
                opacity=0.3,
                colorscale=[[0, 'yellow'], [1, 'yellow']],
                showscale=False,
                name='Risk Threshold'
            ))
            
            fig.update_layout(
                title={
                    'text': title,
                    'x': 0.5,
                    'font': {'size': 16, 'color': '#2E86AB'}
                },
                scene={
                    'xaxis': {
                        'title': 'Probability',
                        'range': [0, 1],
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'yaxis': {
                        'title': 'Impact',
                        'range': [0, 1],
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'zaxis': {
                        'title': 'Mitigation Cost (€k)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'camera': {
                        'eye': {'x': 1.5, 'y': 1.5, 'z': 1.5}
                    }
                },
                width=800,
                height=600,
                **self.default_layout
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id="risk_analysis_3d")
            
        except Exception as e:
            self.logger.error(f"Error creating 3D risk analysis: {e}")
            return self._create_fallback_html(title, "Error creating 3D chart")
    
    def create_financial_landscape_3d(self, financial_data: Dict[str, Any],
                                     title: str = "Financial Landscape 3D") -> str:
        """Create comprehensive 3D financial landscape visualization."""
        if not PLOTLY_AVAILABLE:
            return self._create_fallback_html(title, "Financial Landscape 3D")
        
        try:
            fig = make_subplots(
                rows=1, cols=1,
                specs=[[{'type': 'scatter3d'}]],
                subplot_titles=[title]
            )
            
            # Revenue surface
            capacity_range = np.linspace(5, 50, 20)
            price_range = np.linspace(0.03, 0.08, 20)
            capacity_mesh, price_mesh = np.meshgrid(capacity_range, price_range)
            
            # Calculate revenue surface (simplified)
            capacity_factor = 0.25
            revenue_surface = capacity_mesh * capacity_factor * 8760 * price_mesh * 1000 / 1000000  # Revenue in M€
            
            fig.add_trace(go.Surface(
                x=capacity_mesh,
                y=price_mesh,
                z=revenue_surface,
                colorscale='Blues',
                opacity=0.7,
                name='Revenue Surface',
                showscale=False
            ))
            
            # Cost surface
            capex_per_mw = 1.2  # M€/MW
            cost_surface = capacity_mesh * capex_per_mw
            
            fig.add_trace(go.Surface(
                x=capacity_mesh,
                y=price_mesh,
                z=cost_surface,
                colorscale='Reds',
                opacity=0.7,
                name='Cost Surface',
                showscale=False
            ))
            
            # Profitability points
            profitable_points = revenue_surface > cost_surface
            profitable_x = capacity_mesh[profitable_points]
            profitable_y = price_mesh[profitable_points]
            profitable_z = revenue_surface[profitable_points]
            
            if len(profitable_x) > 0:
                fig.add_trace(go.Scatter3d(
                    x=profitable_x,
                    y=profitable_y,
                    z=profitable_z,
                    mode='markers',
                    marker={
                        'size': 5,
                        'color': 'green',
                        'opacity': 0.8
                    },
                    name='Profitable Region'
                ))
            
            fig.update_layout(
                title={
                    'text': title,
                    'x': 0.5,
                    'font': {'size': 16, 'color': '#2E86AB'}
                },
                scene={
                    'xaxis': {
                        'title': 'Capacity (MW)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'yaxis': {
                        'title': 'PPA Price (€/kWh)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'zaxis': {
                        'title': 'Value (M€)',
                        'backgroundcolor': "rgb(230, 230,230)",
                        'gridcolor': "white",
                        'showbackground': True
                    },
                    'camera': {
                        'eye': {'x': 1.5, 'y': 1.5, 'z': 1.5}
                    }
                },
                width=900,
                height=700,
                **self.default_layout
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id="financial_landscape_3d")
            
        except Exception as e:
            self.logger.error(f"Error creating financial landscape 3D: {e}")
            return self._create_fallback_html(title, "Error creating 3D chart")
    
    def _create_fallback_html(self, title: str, content: str) -> str:
        """Create fallback HTML when Plotly is not available."""
        return f"""
        <div style="border: 2px dashed #ccc; padding: 40px; text-align: center; border-radius: 10px; margin: 20px 0;">
            <h3 style="color: #666; margin-bottom: 20px;">{title}</h3>
            <p style="color: #888; font-size: 16px;">{content}</p>
            <p style="color: #aaa; font-size: 14px; margin-top: 15px;">
                📊 Install Plotly to enable interactive 3D visualizations:<br>
                <code style="background: #f5f5f5; padding: 5px; border-radius: 3px;">pip install plotly</code>
            </p>
        </div>
        """
    
    def export_chart_data(self, chart_data: Dict[str, Any], format: str = 'json') -> str:
        """Export chart data in various formats."""
        try:
            if format.lower() == 'json':
                return json.dumps(chart_data, indent=2, default=str)
            elif format.lower() == 'csv' and 'DataFrame' in str(type(chart_data)):
                return chart_data.to_csv()
            else:
                return str(chart_data)
        except Exception as e:
            self.logger.error(f"Error exporting chart data: {e}")
            return f"Error exporting data: {e}"


# Global service instance
_advanced_3d_charts_service: Optional[Advanced3DChartsService] = None

def get_3d_charts_service() -> Advanced3DChartsService:
    """Get global 3D charts service instance."""
    global _advanced_3d_charts_service
    if _advanced_3d_charts_service is None:
        _advanced_3d_charts_service = Advanced3DChartsService()
    return _advanced_3d_charts_service


# Convenience functions for common 3D charts
def create_sensitivity_analysis_3d(x_param: str, y_param: str, 
                                  base_assumptions: Dict[str, Any]) -> str:
    """Create 3D sensitivity analysis with parameter variations."""
    service = get_3d_charts_service()
    
    # Generate parameter ranges
    x_values = np.linspace(0.8, 1.2, 20)  # ±20% variation
    y_values = np.linspace(0.8, 1.2, 20)
    
    # Create result surface (simplified example)
    z_values = np.zeros((len(y_values), len(x_values)))
    base_irr = base_assumptions.get('expected_irr', 0.12)
    
    for i, y_mult in enumerate(y_values):
        for j, x_mult in enumerate(x_values):
            # More realistic IRR calculation based on parameter changes
            # Consider non-linear effects and parameter interactions

            # Production impact (affects revenue)
            production_impact = (x_mult - 1) * 0.08  # 8% IRR change per 20% production change

            # Price impact (affects revenue)
            price_impact = (y_mult - 1) * 0.06  # 6% IRR change per 20% price change

            # Interaction effect (diminishing returns)
            interaction_effect = (x_mult - 1) * (y_mult - 1) * 0.02

            # Combined IRR change with realistic bounds
            total_irr_change = production_impact + price_impact + interaction_effect

            # Apply realistic bounds (IRR typically ranges 5-25% for renewable projects)
            new_irr = base_irr + total_irr_change
            new_irr = max(0.05, min(0.25, new_irr))  # Bound between 5% and 25%

            z_values[i, j] = new_irr * 100
    
    return service.create_sensitivity_surface(
        x_param, x_values, y_param, y_values, z_values,
        f"3D Sensitivity: {x_param} vs {y_param}"
    )


def create_monte_carlo_3d_view(mc_results: Dict[str, Any]) -> str:
    """Create 3D Monte Carlo results visualization."""
    service = get_3d_charts_service()
    return service.create_monte_carlo_distribution_3d(mc_results)


def create_scenario_analysis_3d(scenarios: Dict[str, Dict[str, float]]) -> str:
    """Create 3D scenario comparison visualization."""
    service = get_3d_charts_service()
    return service.create_scenario_comparison_3d(scenarios)