"""
Project Setup View
==================

View component for project setup and configuration.
"""

import flet as ft
import logging
import time
from typing import Dict, Any, Optional, List

from .base_view import BaseView
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from components.forms.client_profile_form import ClientProfileForm
from components.forms.project_params_form import ProjectParamsForm
from components.ui.modern_history_dialog import ModernHistoryDialog
from components.ui.location_selection_widget import LocationSelectionWidget


class ProjectSetupView(BaseView):
    """View for project setup and configuration."""

    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.client_profile = ClientProfile()
        self.project_assumptions = EnhancedProjectAssumptions()

        # Initialize logger for this view
        self.history_logger = logging.getLogger(f"{__name__}.ProjectSetupView.History")
        self.history_logger.setLevel(logging.DEBUG)
        
        # Initialize modern history dialog
        self.modern_history_dialog = ModernHistoryDialog(page)
        self.modern_history_dialog.set_restore_callback(self._handle_version_restore)
        self.modern_history_dialog.set_close_callback(self._handle_history_close)

        # Form components
        self.client_form: Optional[ClientProfileForm] = None
        self.params_form: Optional[ProjectParamsForm] = None
        
        # Location selection widget
        self.location_widget: Optional[LocationSelectionWidget] = None
        self.selected_locations: List[str] = ["Ouarzazate", "Dakhla"]  # Default selection
    
    def build_content(self) -> ft.Control:
        """Build the project setup view content."""

        # Modern header with gradient background
        header = self._create_modern_header()

        # Client Profile Form with modern styling
        self.client_form = ClientProfileForm(self.client_profile)
        self.client_form.on_data_changed = self._on_client_data_changed

        client_card = self._create_modern_card(
            "Client Profile",
            "Company and contact information",
            self.client_form.build(),
            icon=ft.Icons.BUSINESS_CENTER,
            color_scheme="blue"
        )

        # Project Parameters Form with modern styling
        self.params_form = ProjectParamsForm(self.project_assumptions)
        self.params_form.on_data_changed = self._on_params_data_changed

        params_card = self._create_modern_card(
            "Project Parameters",
            "Technical and financial configuration",
            self.params_form.build(),
            icon=ft.Icons.ENGINEERING,
            color_scheme="green"
        )

        # Location Selection Widget with modern styling
        location_card = self._create_modern_location_card()

        # Modern validation status
        validation_status = self._create_modern_validation_status()

        # Modern action buttons
        action_buttons = self._create_modern_action_buttons()

        # Enhanced analysis button with modern styling
        enhanced_analysis_button = self._create_modern_analysis_button()

        return ft.Container(
            content=ft.Column([
                header,
                ft.Container(height=20),
                client_card,
                ft.Container(height=20),
                params_card,
                ft.Container(height=20),
                location_card,
                ft.Container(height=20),
                validation_status,
                ft.Container(height=20),
                action_buttons,
                ft.Container(height=20),
                enhanced_analysis_button,
                ft.Container(height=40)  # Bottom padding
            ], scroll=ft.ScrollMode.AUTO),
            padding=20,
            bgcolor="#FAFBFC",  # Modern light background
            expand=True
        )
    
    def _create_modern_header(self) -> ft.Container:
        """Create modern header with gradient background."""
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.SETTINGS_APPLICATIONS, size=32, color=ft.Colors.WHITE),
                    ft.Container(width=15),
                    ft.Column([
                        ft.Text(
                            "Project Setup",
                            size=28,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.WHITE
                        ),
                        ft.Text(
                            "Configure client information and project parameters",
                            size=14,
                            color=ft.Colors.WHITE70
                        )
                    ], spacing=2)
                ], alignment=ft.MainAxisAlignment.START)
            ]),
            padding=30,
            bgcolor=ft.Colors.BLUE_700,
            border_radius=ft.border_radius.only(bottom_left=15, bottom_right=15),
            gradient=ft.LinearGradient(
                colors=[ft.Colors.BLUE_700, ft.Colors.BLUE_900],
                begin=ft.alignment.top_left,
                end=ft.alignment.bottom_right
            )
        )

    def _create_modern_card(self, title: str, subtitle: str, content: ft.Control,
                           icon: str, color_scheme: str = "blue") -> ft.Card:
        """Create a modern card with enhanced styling."""

        # Color schemes for different card types
        color_schemes = {
            "blue": {
                "primary": ft.Colors.BLUE_600,
                "light": ft.Colors.BLUE_50,
                "border": ft.Colors.BLUE_200
            },
            "green": {
                "primary": ft.Colors.GREEN_600,
                "light": ft.Colors.GREEN_50,
                "border": ft.Colors.GREEN_200
            },
            "purple": {
                "primary": ft.Colors.PURPLE_600,
                "light": ft.Colors.PURPLE_50,
                "border": ft.Colors.PURPLE_200
            },
            "orange": {
                "primary": ft.Colors.ORANGE_600,
                "light": ft.Colors.ORANGE_50,
                "border": ft.Colors.ORANGE_200
            }
        }

        colors = color_schemes.get(color_scheme, color_schemes["blue"])

        # Modern card header
        header = ft.Container(
            content=ft.Row([
                ft.Container(
                    content=ft.Icon(icon, color=ft.Colors.WHITE, size=24),
                    width=50,
                    height=50,
                    bgcolor=colors["primary"],
                    border_radius=25,
                    alignment=ft.alignment.center
                ),
                ft.Container(width=15),
                ft.Column([
                    ft.Text(title, size=20, weight=ft.FontWeight.BOLD, color=colors["primary"]),
                    ft.Text(subtitle, size=13, color=ft.Colors.GREY_600)
                ], spacing=2)
            ]),
            padding=ft.padding.only(left=25, right=25, top=25, bottom=15)
        )

        # Card content with modern styling
        card_content = ft.Container(
            content=content,
            padding=ft.padding.only(left=25, right=25, bottom=25)
        )

        return ft.Card(
            content=ft.Column([header, card_content]),
            elevation=3,
            surface_tint_color=colors["light"],
            color=ft.Colors.WHITE,
            margin=0
        )

    def _create_modern_location_card(self) -> ft.Card:
        """Create modern location selection card."""
        # Use the existing location selection card method for now
        # to maintain compatibility with existing functionality
        return self._create_location_selection_card_modern_styled()

    def _create_location_selection_card_modern_styled(self) -> ft.Card:
        """Create location selection card with modern styling."""
        # Initialize location widget if not exists
        if not self.location_widget:
            self.location_widget = LocationSelectionWidget(self.page, compact_mode=True)
            self.location_widget.set_selected_locations(self.selected_locations)
            self.location_widget.set_selection_callback(self._on_location_selection_changed)

        return self._create_modern_card(
            "Location Analysis",
            "Select locations for comparative analysis",
            self.location_widget.build(),
            icon=ft.Icons.LOCATION_ON,
            color_scheme="orange"
        )

    def _create_modern_validation_status(self) -> ft.Container:
        """Create modern validation status display."""
        return ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.VERIFIED, color=ft.Colors.GREEN_600, size=20),
                ft.Text(
                    "Configuration Status: Ready for Analysis",
                    size=14,
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.GREEN_700
                )
            ]),
            padding=15,
            bgcolor=ft.Colors.GREEN_50,
            border=ft.border.all(1, ft.Colors.GREEN_200),
            border_radius=10
        )

    def _create_modern_action_buttons(self) -> ft.Row:
        """Create modern action buttons row."""
        history_btn = ft.ElevatedButton(
            content=ft.Row([
                ft.Icon(ft.Icons.HISTORY, size=18),
                ft.Text("View History", size=14, weight=ft.FontWeight.W_500)
            ], spacing=8, tight=True),
            on_click=self._on_show_history,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.PURPLE_600,
                color=ft.Colors.WHITE,
                elevation=2,
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=ft.padding.symmetric(horizontal=20, vertical=12)
            )
        )

        return ft.Row([history_btn], alignment=ft.MainAxisAlignment.CENTER)

    def _create_modern_analysis_button(self) -> ft.Container:
        """Create modern enhanced analysis button."""
        return ft.Container(
            content=ft.ElevatedButton(
                content=ft.Row([
                    ft.Icon(ft.Icons.ANALYTICS, size=24, color=ft.Colors.WHITE),
                    ft.Container(width=10),
                    ft.Column([
                        ft.Text(
                            "Start Enhanced Analysis",
                            size=16,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.WHITE
                        ),
                        ft.Text(
                            "Run comprehensive financial modeling with location intelligence",
                            size=12,
                            color=ft.Colors.WHITE70
                        )
                    ], spacing=2)
                ], tight=True),
                on_click=self._on_enhanced_analysis,
                style=ft.ButtonStyle(
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE,
                    elevation=4,
                    shape=ft.RoundedRectangleBorder(radius=12),
                    padding=ft.padding.all(20)
                ),
                height=80
            ),
            alignment=ft.alignment.center
        )
    
    def _create_comprehensive_button(self) -> ft.Card:
        """Create analysis button."""
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.ANALYTICS, color=ft.Colors.PURPLE_600),
                        ft.Text("Financial Analysis & Reports",
                               size=18, weight=ft.FontWeight.BOLD)
                    ]),
                    ft.Text(
                        "Run comprehensive financial analysis with ML predictions, risk analysis, "
                        "location comparison, and generate professional reports",
                        size=12, color=ft.Colors.GREY_700
                    ),
                    ft.ElevatedButton(
                        "🚀 Run Analysis",
                        icon=ft.Icons.PLAY_ARROW,
                        on_click=self._on_run_analysis,
                        bgcolor=ft.Colors.PURPLE_600,
                        color=ft.Colors.WHITE,
                        width=300,
                        height=50,
                        style=ft.ButtonStyle(
                            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.BOLD)
                        )
                    )
                ]),
                padding=20,
                bgcolor=ft.Colors.PURPLE_50
            )
        )
    
    def _create_validation_status(self) -> ft.Container:
        """Create validation status display."""
        validation_results = self.project_assumptions.get_validation_status()
        
        if validation_results['is_valid']:
            status_color = ft.Colors.GREEN
            status_icon = ft.Icons.CHECK_CIRCLE
            status_text = "Configuration Valid"
        else:
            status_color = ft.Colors.RED
            status_icon = ft.Icons.ERROR
            status_text = f"Validation Issues ({validation_results['error_count']} errors)"
        
        status_content = ft.Row([
            ft.Icon(status_icon, color=status_color),
            ft.Text(status_text, color=status_color, weight=ft.FontWeight.BOLD)
        ])
        
        # Add error details if any
        if not validation_results['is_valid']:
            error_details = ft.Column([
                ft.Text("Validation Errors:", size=14, weight=ft.FontWeight.BOLD),
                *[ft.Text(f"• {error}", size=12, color=ft.Colors.RED_700) 
                  for error in validation_results['errors'].values()]
            ])
            
            status_content = ft.Column([
                status_content,
                ft.Divider(height=10),
                error_details
            ])
        
        # Add warnings if any
        warnings = validation_results.get('warnings', {})
        if warnings:
            warning_details = ft.Column([
                ft.Text("Warnings:", size=14, weight=ft.FontWeight.BOLD),
                *[ft.Text(f"• {warning}", size=12, color=ft.Colors.ORANGE_700) 
                  for warning in warnings.values()]
            ])
            
            if isinstance(status_content, ft.Row):
                status_content = ft.Column([status_content])
            
            status_content.controls.extend([
                ft.Divider(height=10),
                warning_details
            ])
        
        return ft.Container(
            content=status_content,
            padding=15,
            bgcolor=ft.Colors.GREY_50,
            border_radius=8,
            border=ft.border.all(1, status_color)
        )
    
    def _on_client_data_changed(self, field: str, value: Any):
        """Handle client profile data changes."""
        setattr(self.client_profile, field, value)
        self.notify_data_changed("client_profile", self.client_profile)
        self.refresh()  # Refresh to update validation status
    
    def _on_params_data_changed(self, field: str, value: Any):
        """Handle project parameters data changes with auto-save."""
        print(f"DEBUG: Project setup received field change - {field} = '{value}'")

        # Get enhanced integration service for auto-save and undo/redo
        try:
            from services.enhanced_integration_service import get_integration_service
            enhanced_service = get_integration_service()
            
            # Record undo command if service available
            if enhanced_service.undo_redo_service:
                from services.undo_redo_service import StateChangeCommand
                old_value = getattr(self.project_assumptions, field, None)
                command = StateChangeCommand(
                    target=self.project_assumptions,
                    property_name=field,
                    old_value=old_value,
                    new_value=value,
                    description=f"Change {field} from {old_value} to {value}"
                )
                enhanced_service.undo_redo_service.execute_command(command)
            
        except Exception as e:
            # Fallback to standard behavior if enhanced service unavailable
            print(f"Enhanced features unavailable: {e}")
        
        # Update data
        setattr(self.project_assumptions, field, value)
        self.project_assumptions.validate_all()  # Re-validate
        self.notify_data_changed("project_assumptions", self.project_assumptions)
        
        # Auto-save if enhanced service available
        try:
            # Check debounce timing
            if hasattr(self, '_last_auto_save_time'):
                if time.time() - self._last_auto_save_time < 2:  # Debounce auto-save
                    return

            if enhanced_service.persistence_service and self.client_profile.is_complete():
                project_id = self.client_profile.get_clean_company_name()
                enhanced_service.save_project_with_versioning(
                    project_id=project_id,
                    project_data={
                        'client_profile': self.client_profile.to_dict(),
                        'project_assumptions': self.project_assumptions.to_dict()
                    }
                )
                self._last_auto_save_time = time.time()
                print(f"Auto-saved project: {project_id}")
        except Exception as e:
            print(f"Auto-save failed: {e}")
        
        self.refresh()  # Refresh to update validation status
    
    def _on_show_history(self, e):
        """Handle show history action."""
        self.history_logger.info("HISTORY: Button clicked by user")

        try:
            # Log client profile status
            self.history_logger.debug(f"Client profile complete: {self.client_profile.is_complete()}")
            self.history_logger.debug(f"Client profile data: company='{self.client_profile.company_name}', "
                                    f"client='{self.client_profile.client_name}', "
                                    f"project='{self.client_profile.project_name}'")

            # Get project ID - use company name if available, otherwise use a default
            if self.client_profile.company_name:
                project_id = self.client_profile.get_clean_company_name()
                self.history_logger.info(f"HISTORY: Opening history for project ID: '{project_id}'")
            else:
                # Show all projects if no specific company name
                project_id = None
                self.history_logger.info("HISTORY: Opening general project history (no specific project)")

            self.page.run_task(self._show_project_history_dialog, project_id)

        except Exception as e:
            self.history_logger.error(f"ERROR: Exception in _on_show_history: {e}")
            import traceback
            self.history_logger.error(f"Stack trace: {traceback.format_exc()}")
            self.show_error(f"Error opening history: {str(e)}")

    async def _show_project_history_dialog(self, project_id: str = None):
        """Show modern project history dialog."""
        if project_id:
            self.history_logger.info(f"DIALOG: Starting modern history dialog for project: '{project_id}'")
        else:
            self.history_logger.info("DIALOG: Starting general modern history dialog for all projects")

        try:
            # Get persistence service
            self.history_logger.debug("SERVICE: Importing enhanced integration service")
            from services.enhanced_integration_service import get_integration_service
            enhanced_service = get_integration_service()
            self.history_logger.debug(f"SERVICE: Enhanced service obtained: {type(enhanced_service).__name__}")

            if not enhanced_service.persistence_service:
                self.history_logger.error("ERROR: Persistence service not available in enhanced service")
                self.show_error("Persistence service not available")
                return

            if project_id:
                self.history_logger.debug(f"DATA: Querying project versions for: '{project_id}'")
                # Get project versions for specific project
                versions = enhanced_service.persistence_service.get_project_versions(project_id)
                self.history_logger.info(f"DATA: Found {len(versions)} version(s) for project '{project_id}'")
                title = f"History: {project_id}"
            else:
                self.history_logger.debug("DATA: Querying all project versions")
                # Get all project versions
                versions = self._get_all_project_versions(enhanced_service.persistence_service)
                self.history_logger.info(f"DATA: Found {len(versions)} total version(s) across all projects")
                title = "All Project History"

            # Log version details
            for i, version in enumerate(versions):
                self.history_logger.debug(f"   Version {version.get('version', 'unknown')}: "
                                        f"created={version.get('created_at', 'unknown')}, "
                                        f"size={version.get('file_size', 0)} bytes, "
                                        f"comment='{version.get('comment', 'no comment')}'")

            if not versions:
                if project_id:
                    self.history_logger.info("INFO: No versions found for specific project - showing notification to user")
                    self.show_notification("No project history found for this project", ft.Colors.BLUE)
                else:
                    self.history_logger.info("INFO: No versions found for any project - showing notification to user")
                    self.show_notification("No project history found", ft.Colors.BLUE)
                return

            self.history_logger.info(f"UI: Showing modern history dialog for {len(versions)} versions")
            # Show modern history dialog
            await self.modern_history_dialog.show(project_id or "All Projects", versions, title)

        except Exception as e:
            self.history_logger.error(f"ERROR: Exception in _show_project_history_dialog: {e}")
            import traceback
            self.history_logger.error(f"Stack trace: {traceback.format_exc()}")
            self.show_error(f"Error loading project history: {str(e)}")
    
    def _handle_version_restore(self, project_id: str, version: int):
        """Handle version restoration from modern dialog."""
        self.history_logger.info(f"RESTORE: Modern dialog requested restore of version {version} for project '{project_id}'")
        self._restore_version(project_id, version)
    
    def _handle_history_close(self):
        """Handle history dialog close and ensure UI is properly refreshed."""
        self.history_logger.info("UI: Modern history dialog closed")
        
        # Force a final refresh to ensure UI is not frozen
        try:
            self.refresh()
            self.page.update()
            self.history_logger.debug("UI: Final refresh after dialog close completed")
        except Exception as e:
            self.history_logger.warning(f"UI: Error during final refresh: {e}")

    def _create_history_dialog(self, project_id: str, versions: list):
        """Create and show the enhanced project history dialog with financial details."""
        self.history_logger.info(f"UI: Creating enhanced history dialog for {len(versions)} versions")

        # Create enhanced version list items with financial preview
        version_items = []
        for version_info in versions:
            version_num = version_info['version']
            created_at = version_info['created_at']
            comment = version_info.get('comment', 'No comment')
            file_size = version_info.get('file_size', 0)
            version_project_id = version_info.get('project_id', project_id)

            # Get financial preview for this version
            financial_preview = self._get_version_financial_preview(version_project_id, version_num)

            # Format date
            try:
                from datetime import datetime
                if isinstance(created_at, str):
                    date_obj = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                else:
                    date_obj = created_at
                formatted_date = date_obj.strftime("%Y-%m-%d %H:%M:%S")
            except:
                formatted_date = str(created_at)

            # Format file size
            if file_size > 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size} bytes"

            # Add visual indicator for current version
            is_current = version_num == versions[0]['version'] if versions else False

            # Create financial summary display
            financial_display = self._create_financial_summary_display(financial_preview)

            version_item = ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        # Header section
                        ft.Row([
                            ft.Icon(
                                ft.Icons.STAR if is_current else ft.Icons.HISTORY,
                                color=ft.Colors.AMBER if is_current else ft.Colors.PURPLE_600
                            ),
                            ft.Column([
                                ft.Row([
                                    ft.Text(f"Version {version_num}",
                                           size=16, weight=ft.FontWeight.BOLD),
                                    ft.Container(
                                        content=ft.Text("CURRENT", size=10, color=ft.Colors.WHITE),
                                        bgcolor=ft.Colors.AMBER,
                                        padding=ft.padding.symmetric(horizontal=8, vertical=2),
                                        border_radius=10
                                    ) if is_current else ft.Container()
                                ]),
                                ft.Text(formatted_date, size=12, color=ft.Colors.GREY_600),
                                # Show project name if we're displaying multiple projects
                                ft.Text(f"Project: {version_project_id}",
                                       size=11, color=ft.Colors.BLUE_600, weight=ft.FontWeight.W_500)
                                if not project_id and version_project_id else ft.Container()
                            ], spacing=2),
                        ], alignment=ft.MainAxisAlignment.START, spacing=10),
                        
                        # Comment section
                        ft.Container(height=5),
                        ft.Text(comment or "No comment", size=12, color=ft.Colors.GREY_700, italic=not comment),
                        
                        # Financial preview section (new)
                        ft.Divider(height=10),
                        ft.ExpansionTile(
                            title=ft.Text("📊 Financial Data Preview", size=13, weight=ft.FontWeight.BOLD),
                            subtitle=ft.Text("Click to see what financial data will be restored", size=11, color=ft.Colors.GREY_600),
                            initially_expanded=False,
                            controls=[financial_display],
                            bgcolor=ft.Colors.BLUE_50,
                            collapsed_bgcolor=ft.Colors.GREY_50
                        ),
                        
                        # File info and actions
                        ft.Row([
                            ft.Icon(ft.Icons.STORAGE, size=16, color=ft.Colors.GREY_500),
                            ft.Text(f"{size_str}", size=10, color=ft.Colors.GREY_500)
                        ], spacing=5),
                        ft.Container(height=10),
                        ft.Row([
                            ft.ElevatedButton(
                                "🔄 Restore All Data",
                                icon=ft.Icons.RESTORE,
                                on_click=lambda e, v=version_num, p=version_project_id: self._restore_version(p, v),
                                bgcolor=ft.Colors.PURPLE_600,
                                color=ft.Colors.WHITE,
                                disabled=is_current,
                                tooltip="Restore all client profile, financial parameters, grants, and technical data"
                            ) if not is_current else ft.Container(
                                content=ft.Text(
                                    "✅ This is the current version",
                                    size=12,
                                    color=ft.Colors.GREEN_600,
                                    weight=ft.FontWeight.BOLD
                                ),
                                bgcolor=ft.Colors.GREEN_50,
                                padding=10,
                                border_radius=5
                            )
                        ], alignment=ft.MainAxisAlignment.END)
                    ]),
                    padding=15,
                    bgcolor=ft.Colors.AMBER_50 if is_current else None
                ),
                elevation=3 if is_current else 2
            )
            version_items.append(version_item)

        # Calculate enhanced statistics
        total_size = sum(v.get('file_size', 0) for v in versions)
        if total_size > 1024 * 1024:
            total_size_str = f"{total_size / (1024 * 1024):.1f} MB"
        elif total_size > 1024:
            total_size_str = f"{total_size / 1024:.1f} KB"
        else:
            total_size_str = f"{total_size} bytes"

        # Get date range
        if versions:
            oldest_date = versions[-1]['created_at']
            newest_date = versions[0]['created_at']
            try:
                from datetime import datetime
                if isinstance(oldest_date, str):
                    oldest_obj = datetime.fromisoformat(oldest_date.replace('Z', '+00:00'))
                    oldest_formatted = oldest_obj.strftime("%Y-%m-%d")
                else:
                    oldest_formatted = str(oldest_date)
            except:
                oldest_formatted = str(oldest_date)

        # Create enhanced dialog content
        dialog_content = ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.HISTORY, color=ft.Colors.PURPLE_600, size=24),
                ft.Text("Project History - Complete Data Restoration", size=20, weight=ft.FontWeight.BOLD)
            ]),
            ft.Divider(),
            
            # Information banner
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.INFO, color=ft.Colors.BLUE_600, size=16),
                        ft.Text("Each version contains complete project data:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700)
                    ], spacing=5),
                    ft.Text("• Client profile information (company, contact details)", size=12, color=ft.Colors.GREY_700),
                    ft.Text("• Financial parameters (CAPEX, OPEX, debt ratios, interest rates)", size=12, color=ft.Colors.GREY_700),
                    ft.Text("• Grants & incentives (Italian, MASEN, Connection, SIMEST grants)", size=12, color=ft.Colors.GREY_700),
                    ft.Text("• Technical parameters (capacity, production, degradation)", size=12, color=ft.Colors.GREY_700),
                ], spacing=3),
                padding=12,
                bgcolor=ft.Colors.BLUE_50,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.BLUE_200)
            ),
            
            # Project statistics
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.FOLDER, size=16, color=ft.Colors.BLUE_600),
                        ft.Text(f"Project: {project_id}" if project_id else "All Projects",
                               size=14, weight=ft.FontWeight.BOLD)
                    ], spacing=5),
                    ft.Row([
                        ft.Icon(ft.Icons.ANALYTICS, size=16, color=ft.Colors.GREEN_600),
                        ft.Text(f"{len(versions)} version(s) • Total size: {total_size_str}", size=12, color=ft.Colors.GREY_600)
                    ], spacing=5),
                    ft.Row([
                        ft.Icon(ft.Icons.DATE_RANGE, size=16, color=ft.Colors.ORANGE_600),
                        ft.Text(f"From: {oldest_formatted if versions else 'N/A'}", size=12, color=ft.Colors.GREY_600)
                    ], spacing=5) if versions else ft.Container()
                ], spacing=5),
                padding=10,
                bgcolor=ft.Colors.GREY_50,
                border_radius=8
            ),
            
            ft.Container(height=10),
            ft.Column(
                version_items,
                height=450,
                scroll=ft.ScrollMode.AUTO
            )
        ], width=750, height=650)

        # Create and show dialog
        self.history_dialog = ft.AlertDialog(
            title=ft.Text("Complete Project History"),
            content=dialog_content,
            actions=[
                ft.TextButton("Close", on_click=self._close_history_dialog)
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )

        self.history_logger.info("UI: Adding enhanced history dialog to page overlay and showing")
        self.page.overlay.append(self.history_dialog)
        self.history_dialog.open = True
        self.page.update()
        self.history_logger.info("SUCCESS: Enhanced history dialog displayed successfully")

    def _get_version_financial_preview(self, project_id: str, version: int) -> Dict[str, Any]:
        """Get financial preview data for a specific version."""
        try:
            from services.enhanced_integration_service import get_integration_service
            enhanced_service = get_integration_service()
            if enhanced_service.persistence_service:
                project_data = enhanced_service.persistence_service.load_project(project_id, version)
                if project_data and hasattr(project_data, 'project_assumptions') and project_data.project_assumptions:
                    return project_data.project_assumptions
        except Exception as e:
            self.history_logger.warning(f"Could not load financial preview for version {version}: {e}")
        
        return {}

    def _create_financial_summary_display(self, financial_data: Dict[str, Any]) -> ft.Container:
        """Create a financial summary display for the history dialog."""
        if not financial_data:
            return ft.Container(
                content=ft.Text("No financial data available", color=ft.Colors.GREY_600, italic=True),
                padding=10
            )

        try:
            # Extract key financial metrics
            capacity = financial_data.get('capacity_mw', 0)
            capex = financial_data.get('capex_meur', 0)
            opex = financial_data.get('opex_keuros_year1', 0)
            ppa_price = financial_data.get('ppa_price_eur_kwh', 0)
            
            # Grants
            grant_italy = financial_data.get('grant_meur_italy', 0)
            grant_masen = financial_data.get('grant_meur_masen', 0)
            grant_connection = financial_data.get('grant_meur_connection', 0)
            grant_simest = financial_data.get('grant_meur_simest_africa', 0)
            grant_cri = financial_data.get('grant_meur_cri', 0)
            total_grants = grant_italy + grant_masen + grant_connection + grant_simest + grant_cri
            
            # Financing
            debt_ratio = financial_data.get('debt_ratio', 0)
            interest_rate = financial_data.get('interest_rate', 0)
            
            summary_content = [
                # Technical & Financial Overview
                ft.Row([
                    ft.Column([
                        ft.Text("💡 Technical:", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                        ft.Text(f"Capacity: {capacity:.1f} MW", size=11),
                        ft.Text(f"CAPEX: €{capex:.1f}M", size=11),
                        ft.Text(f"OPEX: €{opex:.0f}k/year", size=11),
                    ], spacing=2),
                    ft.Column([
                        ft.Text("💰 Revenue:", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
                        ft.Text(f"PPA: €{ppa_price:.3f}/kWh", size=11),
                        ft.Text(f"Production: {financial_data.get('production_mwh_year1', 0):.0f} MWh/y", size=11),
                        ft.Text(f"Financing: {debt_ratio:.0%} debt @ {interest_rate:.1%}", size=11),
                    ], spacing=2),
                ], alignment=ft.MainAxisAlignment.START),
                
                ft.Divider(height=8),
                
                # Grants section
                ft.Text("🎁 Grants & Incentives:", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_700),
            ]
            
            if total_grants > 0:
                grant_details = []
                if grant_italy > 0:
                    grant_details.append(f"Italian: €{grant_italy:.1f}M")
                if grant_masen > 0:
                    grant_details.append(f"MASEN: €{grant_masen:.1f}M")
                if grant_connection > 0:
                    grant_details.append(f"Connection: €{grant_connection:.1f}M")
                if grant_simest > 0:
                    grant_details.append(f"SIMEST: €{grant_simest:.1f}M")
                if grant_cri > 0:
                    grant_details.append(f"CRI: €{grant_cri:.1f}M")
                
                summary_content.extend([
                    ft.Text(f"Total Grants: €{total_grants:.1f}M", size=11, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_600),
                    ft.Text(" • ".join(grant_details), size=10, color=ft.Colors.GREY_700),
                ])
            else:
                summary_content.append(ft.Text("No grants configured", size=11, color=ft.Colors.GREY_600, italic=True))

            return ft.Container(
                content=ft.Column(summary_content, spacing=3),
                padding=10,
                bgcolor=ft.Colors.WHITE,
                border_radius=5,
                border=ft.border.all(1, ft.Colors.GREY_300)
            )
            
        except Exception as e:
            return ft.Container(
                content=ft.Text(f"Error displaying financial data: {str(e)}", 
                               color=ft.Colors.RED_600, size=11),
                padding=10
            )

    def _restore_version(self, project_id: str, version: int):
        """Restore a specific project version with enhanced confirmation and comprehensive restoration."""
        self.history_logger.info(f"RESTORE: User requested restore of version {version} for project '{project_id}'")

        # Get version preview for confirmation dialog
        try:
            from services.enhanced_integration_service import get_integration_service
            enhanced_service = get_integration_service()
            if enhanced_service.persistence_service:
                project_data = enhanced_service.persistence_service.load_project(project_id, version)
                if project_data:
                    preview_info = self._generate_version_preview(project_data)
                else:
                    preview_info = "Unable to load version preview"
            else:
                preview_info = "Persistence service unavailable"
        except Exception as e:
            preview_info = f"Error loading preview: {str(e)}"

        # Show enhanced confirmation dialog
        def confirm_restore(e):
            self.history_logger.info(f"RESTORE: User confirmed restore of version {version}")
            confirm_dialog.open = False
            self.page.update()
            self._perform_restore(project_id, version)

        def cancel_restore(e):
            self.history_logger.info(f"RESTORE: User cancelled restore of version {version}")
            confirm_dialog.open = False
            self.page.update()

        confirm_dialog = ft.AlertDialog(
            title=ft.Text(f"Restore Version {version}"),
            content=ft.Container(
                content=ft.Column([
                    ft.Text(
                        "This will restore all project data including:",
                        size=14, weight=ft.FontWeight.BOLD
                    ),
                    ft.Text("• Client profile information", size=12),
                    ft.Text("• All financial parameters (CAPEX, OPEX, PPA prices, etc.)", size=12),
                    ft.Text("• Grants and incentives (Italian, MASEN, Connection, SIMEST)", size=12),
                    ft.Text("• Technical parameters (capacity, production, degradation)", size=12),
                    ft.Text("• Financing structure (debt ratio, interest rates, etc.)", size=12),
                    ft.Divider(height=10),
                    ft.Text("Version Preview:", size=14, weight=ft.FontWeight.BOLD),
                    ft.Container(
                        content=ft.Text(preview_info, size=11, color=ft.Colors.GREY_700),
                        bgcolor=ft.Colors.GREY_100,
                        padding=10,
                        border_radius=5
                    ),
                    ft.Divider(height=10),
                    ft.Text(
                        "⚠️ Any unsaved changes will be lost!",
                        size=12, color=ft.Colors.RED, weight=ft.FontWeight.BOLD
                    )
                ], scroll=ft.ScrollMode.AUTO),
                width=500,
                height=350
            ),
            actions=[
                ft.TextButton("Cancel", on_click=cancel_restore),
                ft.ElevatedButton(
                    "Restore All Data",
                    icon=ft.Icons.RESTORE,
                    on_click=confirm_restore,
                    bgcolor=ft.Colors.PURPLE_600,
                    color=ft.Colors.WHITE
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )

        self.page.overlay.append(confirm_dialog)
        confirm_dialog.open = True
        self.page.update()

    def _generate_version_preview(self, project_data) -> str:
        """Generate a preview of what will be restored from the version."""
        preview_lines = []
        
        try:
            # Client profile preview
            if hasattr(project_data, 'client_profile') and project_data.client_profile:
                client = project_data.client_profile
                company = client.get('company_name', 'Unknown')
                project_name = client.get('project_name', 'Unknown')
                preview_lines.append(f"Client: {company}")
                preview_lines.append(f"Project: {project_name}")
            
            # Financial parameters preview
            if hasattr(project_data, 'project_assumptions') and project_data.project_assumptions:
                assumptions = project_data.project_assumptions
                
                # Key financial metrics
                capacity = assumptions.get('capacity_mw', 0)
                capex = assumptions.get('capex_meur', 0)
                ppa_price = assumptions.get('ppa_price_eur_kwh', 0)
                
                preview_lines.append(f"Capacity: {capacity:.1f} MW")
                preview_lines.append(f"CAPEX: €{capex:.1f}M")
                preview_lines.append(f"PPA Price: €{ppa_price:.3f}/kWh")
                
                # Grants summary
                grants = [
                    assumptions.get('grant_meur_italy', 0),
                    assumptions.get('grant_meur_masen', 0),
                    assumptions.get('grant_meur_connection', 0),
                    assumptions.get('grant_meur_simest_africa', 0)
                ]
                total_grants = sum(grants)
                active_grants = len([g for g in grants if g > 0])
                
                if total_grants > 0:
                    preview_lines.append(f"Grants: €{total_grants:.1f}M ({active_grants} types)")
                else:
                    preview_lines.append("Grants: None")
                
                # Financing structure
                debt_ratio = assumptions.get('debt_ratio', 0)
                interest_rate = assumptions.get('interest_rate', 0)
                preview_lines.append(f"Debt: {debt_ratio:.1%} @ {interest_rate:.1%}")
                
        except Exception as e:
            preview_lines.append(f"Preview error: {str(e)}")
        
        return "\n".join(preview_lines) if preview_lines else "No preview available"

    def _perform_restore(self, project_id: str, version: int):
        """Perform the actual restore operation with comprehensive data restoration."""
        self.history_logger.info(f"RESTORE: Starting comprehensive restore operation for version {version} of project '{project_id}'")

        # Show enhanced loading dialog with progress
        loading_dialog = ft.AlertDialog(
            title=ft.Text("Restoring Version"),
            content=ft.Container(
                content=ft.Column([
                    ft.ProgressRing(),
                    ft.Text(f"Restoring version {version}...", text_align=ft.TextAlign.CENTER),
                    ft.Text("Including all financial parameters and incentives", 
                           text_align=ft.TextAlign.CENTER, size=12, color=ft.Colors.GREY_600)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                width=350,
                height=120,
                alignment=ft.alignment.center
            )
        )

        self.page.overlay.append(loading_dialog)
        loading_dialog.open = True
        self.page.update()

        try:
            # Get persistence service
            self.history_logger.debug("SERVICE: Getting enhanced integration service for restore")
            from services.enhanced_integration_service import get_integration_service
            enhanced_service = get_integration_service()

            if not enhanced_service.persistence_service:
                self.history_logger.error("ERROR: Persistence service not available for restore")
                loading_dialog.open = False
                self.page.update()
                self.show_error("Persistence service not available")
                return

            self.history_logger.debug(f"DATA: Loading project data for version {version}")
            # Load the specific version
            project_data = enhanced_service.persistence_service.load_project(project_id, version)

            if not project_data:
                self.history_logger.error(f"ERROR: Could not load project data for version {version}")
                loading_dialog.open = False
                self.page.update()
                self.show_error(f"Could not load version {version}. The version may be corrupted or no longer available.")
                return

            self.history_logger.info(f"SUCCESS: Successfully loaded project data for version {version}")
            
            # Debug: Log what data is available for restoration
            self.history_logger.debug(f"DEBUG: Available project data keys: {list(vars(project_data).keys())}")
            self.history_logger.debug(f"DEBUG: Client profile available: {project_data.client_profile is not None}")
            self.history_logger.debug(f"DEBUG: Project assumptions available: {project_data.project_assumptions is not None}")
            if project_data.project_assumptions:
                self.history_logger.debug(f"DEBUG: Project assumptions keys: {list(project_data.project_assumptions.keys()) if isinstance(project_data.project_assumptions, dict) else 'Not a dict'}")

            # Track restoration results
            restoration_summary = {
                'client_profile': {'status': 'not_attempted', 'fields_restored': [], 'errors': []},
                'project_assumptions': {'status': 'not_attempted', 'fields_restored': [], 'errors': []},
                'financial_parameters': {'status': 'not_attempted', 'fields_restored': [], 'errors': []},
                'grants_incentives': {'status': 'not_attempted', 'fields_restored': [], 'errors': []}
            }

            # Restore Client Profile Data
            if project_data.client_profile:
                try:
                    self.history_logger.debug("RESTORE: Restoring client profile data")
                    from models.client_profile import ClientProfile
                    
                    # Track what fields are being restored
                    old_profile = self.client_profile.to_dict() if self.client_profile else {}
                    self.client_profile = ClientProfile.from_dict(project_data.client_profile)
                    new_profile = self.client_profile.to_dict()
                    
                    # Track restored fields
                    restored_fields = []
                    for field, value in new_profile.items():
                        if old_profile.get(field) != value:
                            restored_fields.append(field)
                    
                    restoration_summary['client_profile']['fields_restored'] = restored_fields
                    restoration_summary['client_profile']['status'] = 'success'

                    # Validate client profile
                    client_errors = self.client_profile.validate()
                    if client_errors:
                        self.history_logger.warning(f"WARNING: Client profile validation errors: {client_errors}")
                        restoration_summary['client_profile']['errors'] = list(client_errors.values())

                    if self.client_form:
                        self.client_form.update_data(self.client_profile)
                        self.history_logger.debug("SUCCESS: Client form updated with restored data")
                        
                except Exception as e:
                    self.history_logger.error(f"ERROR: Error restoring client profile: {e}")
                    restoration_summary['client_profile']['status'] = 'error'
                    restoration_summary['client_profile']['errors'] = [str(e)]

            # Restore Project Assumptions with Enhanced Financial Parameter Tracking
            if project_data.project_assumptions:
                self.history_logger.debug("DEBUG: Project assumptions found, starting restoration...")
                try:
                    self.history_logger.debug("RESTORE: Restoring project assumptions with comprehensive financial parameters")
                    from models.project_assumptions import EnhancedProjectAssumptions
                    
                    # Track what financial parameters are being restored
                    old_assumptions = self.project_assumptions.to_dict() if self.project_assumptions else {}
                    self.project_assumptions = EnhancedProjectAssumptions.from_dict(project_data.project_assumptions)
                    new_assumptions = self.project_assumptions.to_dict()

                    # Define financial parameter categories for detailed tracking
                    financial_categories = {
                        'basic_financial': ['capex_meur', 'opex_keuros_year1', 'ppa_price_eur_kwh', 'ppa_escalation', 
                                          'debt_ratio', 'interest_rate', 'debt_years', 'discount_rate', 'tax_rate'],
                        'grants_incentives': ['grant_meur_italy', 'grant_meur_masen', 'grant_meur_connection', 'grant_meur_simest_africa', 'grant_meur_cri'],
                        'technical_financial': ['capacity_mw', 'production_mwh_year1', 'degradation_rate', 'project_life_years', 'land_lease_eur_mw_year'],
                        'advanced_financial': ['terminal_growth_rate', 'working_capital_days', 'insurance_rate', 'tax_holiday', 'grace_years']
                    }

                    # Track restored fields by category
                    all_restored_fields = []
                    for category, fields in financial_categories.items():
                        category_restored = []
                        for field in fields:
                            if field in new_assumptions and old_assumptions.get(field) != new_assumptions[field]:
                                category_restored.append(field)
                                all_restored_fields.append(field)
                                self.history_logger.info(f"RESTORED {category}: {field} = {new_assumptions[field]}")
                        
                        if category_restored:
                            restoration_summary[category if category in restoration_summary else 'project_assumptions']['fields_restored'].extend(category_restored)

                    # Log comprehensive financial restoration
                    grants_restored = [f for f in financial_categories['grants_incentives'] if f in all_restored_fields]
                    if grants_restored:
                        total_grants = sum(getattr(self.project_assumptions, grant, 0) for grant in grants_restored)
                        self.history_logger.info(f"GRANTS_RESTORED: {len(grants_restored)} grant types totaling €{total_grants:.2f}M")
                        restoration_summary['grants_incentives']['status'] = 'success'
                        restoration_summary['grants_incentives']['fields_restored'] = grants_restored

                    financial_restored = [f for f in financial_categories['basic_financial'] if f in all_restored_fields]
                    if financial_restored:
                        self.history_logger.info(f"FINANCIAL_RESTORED: {len(financial_restored)} financial parameters")
                        restoration_summary['financial_parameters']['status'] = 'success'
                        restoration_summary['financial_parameters']['fields_restored'] = financial_restored

                    restoration_summary['project_assumptions']['status'] = 'success'
                    restoration_summary['project_assumptions']['fields_restored'] = all_restored_fields

                    # Enhanced validation with detailed reporting
                    assumption_errors = self.project_assumptions.validate_all()
                    if assumption_errors:
                        self.history_logger.warning(f"WARNING: Project assumptions validation errors: {assumption_errors}")
                        restoration_summary['project_assumptions']['errors'] = list(assumption_errors.values())

                    # Update form with restored data
                    if self.params_form:
                        self.params_form.update_data(self.project_assumptions)
                        self.history_logger.debug("SUCCESS: Parameters form updated with restored data")

                except Exception as e:
                    self.history_logger.error(f"ERROR: Error restoring project assumptions: {e}")
                    restoration_summary['project_assumptions']['status'] = 'error'
                    restoration_summary['project_assumptions']['errors'] = [str(e)]
            else:
                self.history_logger.warning("WARNING: No project assumptions data found in saved version - only client profile will be restored")
                restoration_summary['project_assumptions']['status'] = 'skipped'
                restoration_summary['project_assumptions']['errors'] = ['No project assumptions data in saved version']

            # Close loading dialog
            loading_dialog.open = False
            if loading_dialog in self.page.overlay:
                self.page.overlay.remove(loading_dialog)
            self.page.update()

            # Close history dialog first
            self._close_history_dialog(None)
            
            # Comprehensive UI refresh and form synchronization
            self._refresh_after_restore()
            
            # Force multiple UI updates to ensure data persistence
            self.page.update()

            # Generate comprehensive restoration report
            self._show_restoration_summary(version, restoration_summary)

        except Exception as e:
            self.history_logger.error(f"ERROR: Exception during restore operation: {e}")
            import traceback
            self.history_logger.error(f"Stack trace: {traceback.format_exc()}")
            # Close loading dialog on error
            loading_dialog.open = False
            if loading_dialog in self.page.overlay:
                self.page.overlay.remove(loading_dialog)
            self.page.update()
            self.show_error(f"Error restoring version {version}: {str(e)}")

    def _refresh_after_restore(self):
        """Comprehensive UI refresh after data restoration to prevent freezing."""
        self.history_logger.debug("REFRESH: Starting comprehensive UI refresh after restore")
        
        try:
            # 1. Force form data updates with validation
            if hasattr(self, 'client_form') and self.client_form:
                self.history_logger.debug("REFRESH: Updating client form data")
                self.client_form.update_data(self.client_profile)
                # Force form rebuild by clearing and recreating controls
                if hasattr(self.client_form, 'refresh_controls'):
                    self.client_form.refresh_controls()
                
            if hasattr(self, 'params_form') and self.params_form:
                self.history_logger.debug("REFRESH: Updating parameters form data")
                self.params_form.update_data(self.project_assumptions)
                # Force form rebuild
                if hasattr(self.params_form, 'refresh_controls'):
                    self.params_form.refresh_controls()
            
            # 2. Trigger validation updates
            self.project_assumptions.validate_all()
            
            # 3. Notify data change listeners to refresh dependent components
            self.notify_data_changed("client_profile", self.client_profile)
            self.notify_data_changed("project_assumptions", self.project_assumptions)
            
            # 4. Force view refresh multiple times with delays
            self.refresh()
            self.page.update()
            
            # 5. Schedule delayed refresh to ensure UI stability
            import threading
            import time
            
            def delayed_refresh():
                time.sleep(0.1)  # Short delay
                try:
                    self.refresh()
                    self.page.update()
                    self.history_logger.debug("REFRESH: Delayed refresh completed")
                except Exception as e:
                    self.history_logger.warning(f"REFRESH: Delayed refresh failed: {e}")
            
            # Run delayed refresh in background
            refresh_thread = threading.Thread(target=delayed_refresh, daemon=True)
            refresh_thread.start()
            
            # 6. Re-validate and update status displays
            self._force_validation_refresh()
            
            self.history_logger.debug("REFRESH: Comprehensive UI refresh completed successfully")
            
        except Exception as e:
            self.history_logger.error(f"REFRESH: Error during UI refresh: {e}")
            # Fallback to basic refresh
            try:
                self.refresh()
                self.page.update()
            except Exception as fallback_error:
                self.history_logger.error(f"REFRESH: Fallback refresh also failed: {fallback_error}")

    def _force_validation_refresh(self):
        """Force validation status refresh to update UI indicators."""
        try:
            # Trigger validation and form updates
            client_errors = self.client_profile.validate()
            assumption_errors = self.project_assumptions.validate_all()
            
            # Force rebuild of validation status display
            if hasattr(self, 'validation_status_container'):
                # Update validation display
                validation_status = self._create_validation_status()
                self.validation_status_container.content = validation_status.content
                self.validation_status_container.update()
            
            self.history_logger.debug(f"VALIDATION_REFRESH: Client errors: {len(client_errors) if client_errors else 0}, Assumption errors: {len(assumption_errors) if assumption_errors else 0}")
            
        except Exception as e:
            self.history_logger.warning(f"VALIDATION_REFRESH: Failed to refresh validation status: {e}")

    def _show_restoration_summary(self, version: int, restoration_summary: Dict[str, Any]):
        """Show comprehensive restoration summary to user."""
        # Count total restored fields and errors
        total_restored = sum(len(category.get('fields_restored', [])) for category in restoration_summary.values())
        total_errors = sum(len(category.get('errors', [])) for category in restoration_summary.values())
        
        # Determine overall status
        if total_errors == 0:
            status_color = ft.Colors.GREEN
            status_icon = ft.Icons.CHECK_CIRCLE
            status_text = f"Complete Restoration Successful"
        elif total_restored > 0:
            status_color = ft.Colors.ORANGE
            status_icon = ft.Icons.WARNING
            status_text = f"Restoration Completed with Warnings"
        else:
            status_color = ft.Colors.RED
            status_icon = ft.Icons.ERROR
            status_text = f"Restoration Failed"

        # Create detailed summary content
        summary_content = [
            ft.Row([
                ft.Icon(status_icon, color=status_color, size=24),
                ft.Text(status_text, size=18, weight=ft.FontWeight.BOLD, color=status_color)
            ]),
            ft.Divider(),
            ft.Text(f"Version {version} Restoration Summary:", size=16, weight=ft.FontWeight.BOLD),
            ft.Text(f"Total fields restored: {total_restored}", size=14),
        ]

        # Add detailed breakdown by category
        if restoration_summary['client_profile']['fields_restored']:
            client_fields = restoration_summary['client_profile']['fields_restored']
            summary_content.extend([
                ft.Divider(height=10),
                ft.Text("✓ Client Profile Restored:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN),
                ft.Text(f"  • {len(client_fields)} fields: {', '.join(client_fields)}", size=12)
            ])

        if restoration_summary['financial_parameters']['fields_restored']:
            financial_fields = restoration_summary['financial_parameters']['fields_restored']
            summary_content.extend([
                ft.Divider(height=10),
                ft.Text("💰 Financial Parameters Restored:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE),
                ft.Text(f"  • {len(financial_fields)} parameters: {', '.join(financial_fields)}", size=12)
            ])

        if restoration_summary['grants_incentives']['fields_restored']:
            grant_fields = restoration_summary['grants_incentives']['fields_restored']
            # Calculate total grant amount
            total_grants = sum(getattr(self.project_assumptions, grant, 0) for grant in grant_fields)
            summary_content.extend([
                ft.Divider(height=10),
                ft.Text("🎁 Grants & Incentives Restored:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE),
                ft.Text(f"  • {len(grant_fields)} grant types totaling €{total_grants:.2f}M", size=12),
                ft.Text(f"  • Types: {', '.join(grant_fields)}", size=11, color=ft.Colors.GREY_700)
            ])

        # Add technical parameters if any
        technical_fields = [f for f in restoration_summary['project_assumptions']['fields_restored'] 
                          if f not in restoration_summary['financial_parameters']['fields_restored'] 
                          and f not in restoration_summary['grants_incentives']['fields_restored']]
        if technical_fields:
            summary_content.extend([
                ft.Divider(height=10),
                ft.Text("⚙️ Technical Parameters Restored:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.TEAL),
                ft.Text(f"  • {len(technical_fields)} parameters: {', '.join(technical_fields)}", size=12)
            ])

        # Add warnings/errors if any
        all_errors = []
        for category in restoration_summary.values():
            all_errors.extend(category.get('errors', []))
        
        if all_errors:
            summary_content.extend([
                ft.Divider(height=10),
                ft.Text("⚠️ Warnings/Errors:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE),
                *[ft.Text(f"  • {error}", size=12, color=ft.Colors.ORANGE_700) for error in all_errors[:5]]
            ])
            if len(all_errors) > 5:
                summary_content.append(ft.Text(f"  • ... and {len(all_errors)-5} more", size=12, color=ft.Colors.GREY_600))

        # Show notification with summary
        notification_text = f"Successfully restored version {version} with {total_restored} parameters"
        if total_errors > 0:
            notification_text += f" ({total_errors} warnings)"
        
        # Show detailed dialog
        summary_dialog = ft.AlertDialog(
            title=ft.Text("Restoration Complete"),
            content=ft.Container(
                content=ft.Column(summary_content, scroll=ft.ScrollMode.AUTO),
                width=500,
                height=400
            ),
            actions=[
                ft.ElevatedButton(
                    "OK",
                    icon=ft.Icons.CHECK,
                    on_click=lambda e: self._close_summary_dialog(summary_dialog),
                    bgcolor=status_color,
                    color=ft.Colors.WHITE
                )
            ]
        )

        self.page.overlay.append(summary_dialog)
        summary_dialog.open = True
        self.page.update()

        # Also show brief notification
        self.show_notification(notification_text, status_color)
        
        # Log summary for audit trail
        self.history_logger.info(f"RESTORATION_COMPLETE: Version {version} - {total_restored} fields restored, {total_errors} errors")

    def _close_summary_dialog(self, dialog: ft.AlertDialog):
        """Close the restoration summary dialog."""
        dialog.open = False
        self.page.update()

    def _get_all_project_versions(self, persistence_service):
        """Get all project versions across all projects."""
        try:
            # Get all project IDs first
            all_versions = []

            # Try to get all projects from the database
            # This is a simplified approach - in a real implementation,
            # you might want to add a method to get all project IDs
            import sqlite3
            db_path = persistence_service.db_path

            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT project_id FROM project_versions ORDER BY project_id")
                project_ids = [row[0] for row in cursor.fetchall()]

            # Get versions for each project
            for project_id in project_ids:
                project_versions = persistence_service.get_project_versions(project_id)
                # Add project_id to each version for identification
                for version in project_versions:
                    version['project_id'] = project_id
                all_versions.extend(project_versions)

            # Sort by creation date (newest first)
            all_versions.sort(key=lambda x: x.get('created_at', ''), reverse=True)

            return all_versions

        except Exception as e:
            self.history_logger.error(f"ERROR: Failed to get all project versions: {e}")
            return []

    def _close_history_dialog(self, e):
        """Close the history dialog."""
        self.history_logger.info("DIALOG: Closing history dialog")
        if hasattr(self, 'history_dialog'):
            self.history_dialog.open = False
            self.page.update()
            self.history_logger.debug("SUCCESS: History dialog closed successfully")
    

    
    def _create_location_selection_card(self) -> ft.Card:
        """Create location selection card using the location widget."""
        # Initialize location widget if not exists
        if not self.location_widget:
            self.location_widget = LocationSelectionWidget(self.page, compact_mode=True)
            self.location_widget.set_selected_locations(self.selected_locations)
            self.location_widget.set_selection_callback(self._on_location_selection_changed)
        
        return self.create_card(
            "Location Analysis",
            self.location_widget.build(),
            icon=ft.Icons.LOCATION_ON,
            bgcolor=ft.Colors.GREEN_50
        )
    
    def _create_enhanced_analysis_button(self) -> ft.Card:
        """Create enhanced analysis button with location context."""
        # Ensure location widget is initialized and get actual selected locations
        if self.location_widget:
            actual_selected = self.location_widget.get_selected_locations()
            # Sync with internal state
            if actual_selected != self.selected_locations:
                self.selected_locations = actual_selected

        selected_count = len(self.selected_locations)

        # Fallback: if no locations selected, use default
        if selected_count == 0:
            self.selected_locations = ["Ouarzazate"]  # Ensure at least one location
            selected_count = 1
            if self.location_widget:
                self.location_widget.set_selected_locations(self.selected_locations)

        # Determine button state and text
        if selected_count == 0:
            button_text = "⚠️ Select Location(s) First"
            button_color = ft.Colors.GREY_600
            enabled = False
            analysis_type = "Please select at least one location for analysis"
        elif selected_count == 1:
            button_text = f"🚀 Run Analysis ({selected_count} location)"
            button_color = ft.Colors.BLUE_600
            enabled = True
            analysis_type = "Comprehensive financial analysis for selected location"
        else:
            button_text = f"🚀 Run Analysis + Compare ({selected_count} locations)"
            button_color = ft.Colors.PURPLE_600
            enabled = True
            analysis_type = "Multi-location comparison with comprehensive analysis"
        
        # Get location selection summary
        if self.location_widget:
            summary = self.location_widget.get_selection_summary()
            estimated_time = summary.get('estimated_analysis_time', 'N/A')
            regions = summary.get('regions', [])
            region_text = f"Regions: {', '.join(regions)}" if regions else "No regions selected"
        else:
            estimated_time = "N/A"
            region_text = "No locations selected"
        
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.ANALYTICS, color=ft.Colors.PURPLE_600),
                        ft.Text("Financial Analysis & Location Intelligence", 
                               size=18, weight=ft.FontWeight.BOLD)
                    ]),
                    ft.Text(analysis_type, size=12, color=ft.Colors.GREY_700),
                    
                    # Location context info
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.INFO, size=16, color=ft.Colors.BLUE_600),
                                ft.Text("Analysis Context:", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700)
                            ], spacing=5),
                            ft.Text(f"📍 {selected_count} location(s) selected", size=11, color=ft.Colors.GREY_700),
                            ft.Text(f"🌍 {region_text}", size=11, color=ft.Colors.GREY_700),
                            ft.Text(f"⏱️ Estimated time: {estimated_time}", size=11, color=ft.Colors.GREY_700),
                            ft.Text(f"📊 {'Includes comparison analysis' if selected_count > 1 else 'Single location analysis'}", 
                                   size=11, color=ft.Colors.GREY_700)
                        ], spacing=3),
                        padding=10,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=5,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ) if selected_count > 0 else ft.Container(),
                    
                    ft.ElevatedButton(
                        button_text,
                        icon=ft.Icons.ANALYTICS,
                        on_click=self._on_enhanced_analysis,
                        bgcolor=button_color,
                        color=ft.Colors.WHITE,
                        width=400,
                        height=50,
                        disabled=not enabled,
                        style=ft.ButtonStyle(
                            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.BOLD)
                        )
                    )
                ]),
                padding=20,
                bgcolor=ft.Colors.PURPLE_50 if selected_count > 1 else ft.Colors.BLUE_50 if selected_count == 1 else ft.Colors.GREY_50
            )
        )
    
    def _on_location_selection_changed(self, selected_locations: List[str]):
        """Handle location selection changes."""
        self.selected_locations = selected_locations
        self.refresh()  # Refresh to update button state
    
    def _on_location_changed(self, selected_locations: List[str]):
        """Handle location changes (alias for compatibility)."""
        self._on_location_selection_changed(selected_locations)
    
    def _on_enhanced_analysis(self, e):
        """Handle enhanced analysis with location comparison."""
        print(f"DEBUG: Enhanced analysis button clicked")
        print(f"DEBUG: Selected locations: {self.selected_locations}")
        print(f"DEBUG: Stored project location: '{self.project_assumptions.project_location}'")
        print(f"DEBUG: Client profile complete: {self.client_profile.is_complete()}")
        print(f"DEBUG: Project assumptions validated: {self.project_assumptions.is_validated}")

        # Validate before running
        if not self.client_profile.is_complete():
            print("DEBUG: Client profile validation failed")
            self.show_error("Please complete client profile before running analysis")
            return

        if not self.project_assumptions.is_validated:
            print("DEBUG: Project assumptions validation failed")
            print(f"DEBUG: Validation errors: {self.project_assumptions.validation_errors}")
            self.show_error("Please fix validation errors before running analysis")
            return

        if len(self.selected_locations) == 0:
            print("DEBUG: No locations selected")
            self.show_error("Please select at least one location for analysis")
            return
        
        # Determine analysis type based on number of locations
        include_comparison = len(self.selected_locations) > 1

        print(f"DEBUG: All validations passed, requesting analysis...")
        print(f"DEBUG: Include comparison: {include_comparison}")

        # Enhanced analysis request with location data
        self.request_action("run_comprehensive_analysis_with_locations", {
            "client_profile": self.client_profile.to_dict(),
            "assumptions": self.project_assumptions.to_dict(),
            "selected_locations": self.selected_locations,
            "include_location_comparison": include_comparison,
            "analysis_context": {
                "location_count": len(self.selected_locations),
                "comparison_enabled": include_comparison,
                "estimated_time": self.location_widget.get_selection_summary().get('estimated_analysis_time', 'N/A') if self.location_widget else 'N/A'
            }
        })
    
    def _on_run_analysis(self, e):
        """Handle run analysis action - fallback to enhanced analysis."""
        self._on_enhanced_analysis(e)
    
    def get_selected_locations(self) -> List[str]:
        """Get currently selected locations."""
        return self.selected_locations.copy()
    
    def set_selected_locations(self, locations: List[str]):
        """Set selected locations."""
        self.selected_locations = locations
        if self.location_widget:
            self.location_widget.set_selected_locations(locations)
        self.refresh()
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with new data."""
        if "client_profile" in data:
            self.client_profile = ClientProfile.from_dict(data["client_profile"])
            if self.client_form:
                self.client_form.update_data(self.client_profile)
        
        if "project_assumptions" in data:
            self.project_assumptions = EnhancedProjectAssumptions.from_dict(data["project_assumptions"])
            if self.params_form:
                self.params_form.update_data(self.project_assumptions)
        
        self.refresh()
    
    def get_client_profile(self) -> ClientProfile:
        """Get current client profile."""
        return self.client_profile
    
    def get_project_assumptions(self) -> EnhancedProjectAssumptions:
        """Get current project assumptions."""
        return self.project_assumptions
    
    def set_client_profile(self, client_profile: ClientProfile):
        """Set client profile."""
        self.client_profile = client_profile
        if self.client_form:
            self.client_form.update_data(client_profile)
        self.refresh()
    
    def set_project_assumptions(self, assumptions: EnhancedProjectAssumptions):
        """Set project assumptions."""
        self.project_assumptions = assumptions
        if self.params_form:
            self.params_form.update_data(assumptions)
        self.refresh()
    
    def validate_all(self) -> bool:
        """Validate all form data."""
        client_valid = self.client_profile.is_complete()
        params_valid = self.project_assumptions.is_validated
        
        return client_valid and params_valid
    
    def refresh(self):
        """Override refresh to ensure proper UI updates."""
        try:
            # Call parent refresh
            super().refresh()
            
            # Additional refresh logic for forms
            if hasattr(self, 'client_form') and self.client_form:
                # Force client form to update its display
                if hasattr(self.client_form, 'page') and self.client_form.page:
                    self.client_form.page.update()
            
            if hasattr(self, 'params_form') and self.params_form:
                # Force params form to update its display
                if hasattr(self.params_form, 'page') and self.params_form.page:
                    self.params_form.page.update()
            
            # Ensure page is updated
            if self.page:
                self.page.update()
                
        except Exception as e:
            self.history_logger.warning(f"REFRESH: Error during enhanced refresh: {e}")
            # Fallback to basic page update
            try:
                if self.page:
                    self.page.update()
            except Exception as fallback_error:
                self.history_logger.error(f"REFRESH: Fallback page update failed: {fallback_error}")
