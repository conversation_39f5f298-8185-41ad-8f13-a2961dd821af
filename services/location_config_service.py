"""
Location Configuration Service
=============================

Service for managing location-specific technical parameters and adjustments.
Maintains financial parameters constant while adjusting technical specifications.
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import logging

@dataclass
class LocationTechnicalData:
    """Technical data specific to a location."""
    location_name: str
    country: str
    region: str
    
    # Solar resource data
    annual_irradiation_kwh_m2: float  # kWh/m²/year
    capacity_factor_multiplier: float  # Multiplier vs baseline (1.0 = same)
    
    # Cost adjustments (multipliers vs baseline)
    capex_multiplier: float  # 1.0 = baseline, 1.15 = 15% higher
    opex_multiplier: float   # 1.0 = baseline
    land_lease_multiplier: float  # 1.0 = baseline
    
    # Technical adjustments
    degradation_adjustment: float  # Additional degradation (0.0 = baseline)
    
    # Connection costs (EUR/MW)
    grid_connection_cost_eur_mw: float
    
    # Location characteristics
    description: str
    advantages: List[str]
    challenges: List[str]
    
    # Regulatory environment
    regulatory_risk_factor: float  # 1.0 = baseline risk
    
class LocationConfigService:
    """Service for location-specific configurations and comparisons."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._location_database = self._initialize_location_database()
        
    def _initialize_location_database(self) -> Dict[str, LocationTechnicalData]:
        """Initialize the location database with technical parameters."""
        
        locations = {
            # Morocco Premium Locations
            "Ouarzazate": LocationTechnicalData(
                location_name="Ouarzazate",
                country="Morocco",
                region="Morocco Premium",
                annual_irradiation_kwh_m2=2250,
                capacity_factor_multiplier=1.0,  # Baseline
                capex_multiplier=1.0,  # Baseline
                opex_multiplier=1.0,   # Baseline
                land_lease_multiplier=1.0,  # Baseline
                degradation_adjustment=0.0,
                grid_connection_cost_eur_mw=15000,
                description="Established solar hub with excellent infrastructure",
                advantages=[
                    "Proven solar resource (2250 kWh/m²/year)",
                    "Established solar infrastructure",
                    "Good grid connectivity",
                    "Experienced local workforce",
                    "Government support and incentives"
                ],
                challenges=[
                    "Increasing competition for land",
                    "Higher land lease costs in premium areas"
                ],
                regulatory_risk_factor=1.0
            ),
            
            "Dakhla": LocationTechnicalData(
                location_name="Dakhla",
                country="Morocco",
                region="Morocco Premium",
                annual_irradiation_kwh_m2=2420,
                capacity_factor_multiplier=1.08,  # 8% higher than Ouarzazate
                capex_multiplier=1.05,  # Slightly higher due to remoteness
                opex_multiplier=1.10,   # Higher O&M due to location
                land_lease_multiplier=0.85,  # Lower land costs
                degradation_adjustment=-0.002,  # Better conditions
                grid_connection_cost_eur_mw=25000,  # More remote
                description="Exceptional solar resource in southern Morocco",
                advantages=[
                    "Outstanding solar resource (2420 kWh/m²/year)",
                    "Lower land costs",
                    "Less competition",
                    "Excellent wind-solar hybrid potential",
                    "Strategic location for export"
                ],
                challenges=[
                    "Remote location increases costs",
                    "Limited local infrastructure",
                    "Higher grid connection costs",
                    "Logistics challenges"
                ],
                regulatory_risk_factor=1.0
            ),
            
            "Laayoune Advanced": LocationTechnicalData(
                location_name="Laayoune Advanced",
                country="Morocco",
                region="Morocco Extended",
                annual_irradiation_kwh_m2=2380,
                capacity_factor_multiplier=1.06,
                capex_multiplier=1.03,
                opex_multiplier=1.08,
                land_lease_multiplier=0.90,
                degradation_adjustment=-0.001,
                grid_connection_cost_eur_mw=20000,
                description="Advanced solar zone with hybrid potential",
                advantages=[
                    "High solar resource (2380 kWh/m²/year)",
                    "Wind-solar hybrid opportunities",
                    "Growing infrastructure",
                    "Government development focus"
                ],
                challenges=[
                    "Developing infrastructure",
                    "Moderate remoteness",
                    "Dust storms potential"
                ],
                regulatory_risk_factor=1.0
            ),
            
            "Noor Midelt": LocationTechnicalData(
                location_name="Noor Midelt",
                country="Morocco",
                region="Morocco Premium",
                annual_irradiation_kwh_m2=2180,
                capacity_factor_multiplier=0.97,
                capex_multiplier=0.98,  # Slightly lower due to infrastructure
                opex_multiplier=0.95,   # Lower O&M costs
                land_lease_multiplier=1.05,  # Higher land costs
                degradation_adjustment=0.001,  # Slightly higher degradation
                grid_connection_cost_eur_mw=12000,  # Good grid access
                description="Established Noor complex with infrastructure",
                advantages=[
                    "Existing infrastructure from Noor complex",
                    "Good grid connectivity",
                    "Experienced workforce",
                    "Central location",
                    "Government backing"
                ],
                challenges=[
                    "Slightly lower solar resource",
                    "Higher land competition",
                    "Seasonal weather variations"
                ],
                regulatory_risk_factor=0.95  # Lower risk due to established area
            ),
            
            # European Locations
            "Sicily Catania": LocationTechnicalData(
                location_name="Sicily Catania",
                country="Italy",
                region="Europe",
                annual_irradiation_kwh_m2=1950,
                capacity_factor_multiplier=0.87,
                capex_multiplier=1.20,  # Higher European costs
                opex_multiplier=1.35,   # Higher European O&M
                land_lease_multiplier=1.50,  # Much higher land costs
                degradation_adjustment=0.002,  # Slightly higher degradation
                grid_connection_cost_eur_mw=35000,  # Higher European grid costs
                description="Established European market with premium pricing",
                advantages=[
                    "Stable regulatory environment",
                    "Higher PPA prices potential",
                    "EU market access",
                    "Advanced grid infrastructure",
                    "No currency risk for EUR projects"
                ],
                challenges=[
                    "Lower solar resource (1950 kWh/m²/year)",
                    "Higher costs across all categories",
                    "Complex permitting processes",
                    "Higher land costs",
                    "More stringent regulations"
                ],
                regulatory_risk_factor=0.85  # Lower regulatory risk
            ),
            
            "Puglia Brindisi": LocationTechnicalData(
                location_name="Puglia Brindisi",
                country="Italy",
                region="Europe",
                annual_irradiation_kwh_m2=1880,
                capacity_factor_multiplier=0.84,
                capex_multiplier=1.18,
                opex_multiplier=1.32,
                land_lease_multiplier=1.45,
                degradation_adjustment=0.003,
                grid_connection_cost_eur_mw=32000,
                description="Southern Italian location with good infrastructure",
                advantages=[
                    "Good European solar resource",
                    "Excellent grid infrastructure",
                    "Stable market environment",
                    "Experienced supply chain",
                    "EU incentive frameworks"
                ],
                challenges=[
                    "Lower solar resource than Morocco",
                    "High operational costs",
                    "Competitive market",
                    "Complex grid regulations"
                ],
                regulatory_risk_factor=0.85
            ),
            
            "Andalusia Sevilla": LocationTechnicalData(
                location_name="Andalusia Sevilla",
                country="Spain",
                region="Europe",
                annual_irradiation_kwh_m2=2050,
                capacity_factor_multiplier=0.91,
                capex_multiplier=1.15,
                opex_multiplier=1.25,
                land_lease_multiplier=1.40,
                degradation_adjustment=0.001,
                grid_connection_cost_eur_mw=30000,
                description="Spanish solar belt with good resource",
                advantages=[
                    "Best European solar resource (2050 kWh/m²/year)",
                    "Mature solar market",
                    "Good infrastructure",
                    "Favorable regulations",
                    "EU market access"
                ],
                challenges=[
                    "High competition",
                    "Significant cost premiums",
                    "Grid curtailment risks",
                    "Market price volatility"
                ],
                regulatory_risk_factor=0.90
            ),
        }
        
        return locations
    
    def get_location_data(self, location_name: str) -> Optional[LocationTechnicalData]:
        """Get technical data for a specific location."""
        return self._location_database.get(location_name)
    
    def get_available_locations(self) -> List[str]:
        """Get list of all available locations."""
        return list(self._location_database.keys())
    
    def get_locations_by_region(self, region: str) -> List[LocationTechnicalData]:
        """Get all locations in a specific region."""
        return [loc_data for loc_data in self._location_database.values() 
                if loc_data.region == region]
    
    def create_location_scenario(self, base_config: Dict[str, Any], target_location: str) -> Dict[str, Any]:
        """Create a project configuration for a specific location while keeping financial parameters constant.
        
        Args:
            base_config: Base project configuration (from project_assumptions)
            target_location: Target location name
            
        Returns:
            Dict with adjusted configuration for the target location
        """
        location_data = self.get_location_data(target_location)
        if not location_data:
            self.logger.warning(f"Location data not found for {target_location}, using baseline")
            return base_config.copy()
        
        # Create a copy of the base configuration
        scenario_config = base_config.copy()
        
        # Adjust technical parameters based on location
        self._apply_technical_adjustments(scenario_config, location_data)
        
        # Keep financial parameters CONSTANT (this is the key requirement)
        self._preserve_financial_parameters(scenario_config, base_config)
        
        # Add location context
        scenario_config['project_location'] = target_location
        scenario_config['location_technical_data'] = location_data
        
        self.logger.info(f"Created scenario for {target_location} with technical adjustments applied")
        return scenario_config
    
    def _apply_technical_adjustments(self, config: Dict[str, Any], location_data: LocationTechnicalData):
        """Apply location-specific technical adjustments to configuration."""
        
        # Solar resource adjustments
        base_production = config.get('production_mwh_year1', 18000)
        adjusted_production = base_production * location_data.capacity_factor_multiplier
        config['production_mwh_year1'] = adjusted_production
        
        # Cost adjustments
        base_capex = config.get('capex_meur', 8.5)
        adjusted_capex = base_capex * location_data.capex_multiplier
        config['capex_meur'] = adjusted_capex
        
        base_opex = config.get('opex_keuros_year1', 180)
        adjusted_opex = base_opex * location_data.opex_multiplier
        config['opex_keuros_year1'] = adjusted_opex
        
        base_land_lease = config.get('land_lease_eur_mw_year', 2000)
        adjusted_land_lease = base_land_lease * location_data.land_lease_multiplier
        config['land_lease_eur_mw_year'] = adjusted_land_lease
        
        # Degradation adjustment
        base_degradation = config.get('degradation_rate', 0.005)
        adjusted_degradation = base_degradation + location_data.degradation_adjustment
        config['degradation_rate'] = max(0.001, adjusted_degradation)  # Minimum 0.1%
        
        # Add grid connection cost if not already included in CAPEX
        config['grid_connection_cost_eur_mw'] = location_data.grid_connection_cost_eur_mw
        
        self.logger.debug(f"Applied technical adjustments for {location_data.location_name}: "
                         f"Production: {base_production:.0f} -> {adjusted_production:.0f} MWh, "
                         f"CAPEX: {base_capex:.1f} -> {adjusted_capex:.1f} MEUR")
    
    def _preserve_financial_parameters(self, config: Dict[str, Any], base_config: Dict[str, Any]):
        """Ensure all financial parameters remain constant from base configuration."""
        
        # Financial parameters that must remain constant
        constant_financial_params = [
            'ppa_price_eur_kwh', 'ppa_escalation', 'debt_ratio', 'interest_rate',
            'debt_years', 'discount_rate', 'tax_rate', 'tax_holiday', 'grace_years',
            'grant_meur_italy', 'grant_meur_masen', 'grant_meur_connection',
            'grant_meur_simest_africa', 'grant_meur_cri'
        ]
        
        for param in constant_financial_params:
            if param in base_config:
                config[param] = base_config[param]
        
        self.logger.debug("Preserved financial parameters from baseline configuration")
    
    def compare_locations_with_baseline(self, base_config: Dict[str, Any], 
                                      comparison_locations: List[str]) -> Dict[str, Any]:
        """Compare multiple locations against baseline, keeping financial parameters constant.
        
        Args:
            base_config: Baseline project configuration
            comparison_locations: List of locations to compare
            
        Returns:
            Dict containing comparison results with baseline vs alternatives
        """
        baseline_location = base_config.get('project_location', 'Unknown')
        
        # Create scenarios for each location
        scenarios = {}
        scenarios['baseline'] = {
            'config': base_config,
            'location': baseline_location,
            'type': 'baseline'
        }
        
        for location in comparison_locations:
            if location != baseline_location:  # Don't duplicate baseline
                scenario_config = self.create_location_scenario(base_config, location)
                scenarios[location] = {
                    'config': scenario_config,
                    'location': location,
                    'type': 'comparison'
                }
        
        # Calculate financial metrics for each scenario
        comparison_results = {
            'baseline_location': baseline_location,
            'comparison_locations': comparison_locations,
            'scenarios': scenarios,
            'technical_summary': self._create_technical_summary(scenarios),
            'location_data': {loc: self.get_location_data(loc) for loc in comparison_locations}
        }
        
        self.logger.info(f"Generated comparison for {len(scenarios)} scenarios "
                        f"with baseline: {baseline_location}")
        
        return comparison_results
    
    def _create_technical_summary(self, scenarios: Dict[str, Any]) -> Dict[str, Any]:
        """Create technical parameter summary for all scenarios."""
        summary = {}
        
        for scenario_name, scenario_data in scenarios.items():
            config = scenario_data['config']
            location_data = config.get('location_technical_data')
            
            summary[scenario_name] = {
                'production_mwh_year1': config.get('production_mwh_year1', 0),
                'capex_meur': config.get('capex_meur', 0),
                'opex_keuros_year1': config.get('opex_keuros_year1', 0),
                'capacity_factor': config.get('production_mwh_year1', 0) / (config.get('capacity_mw', 1) * 8760),
                'degradation_rate': config.get('degradation_rate', 0),
                'land_lease_eur_mw_year': config.get('land_lease_eur_mw_year', 0),
                'irradiation_kwh_m2': location_data.annual_irradiation_kwh_m2 if location_data else 'N/A',
                'grid_connection_cost': config.get('grid_connection_cost_eur_mw', 0)
            }
        
        return summary
    
    def get_location_comparison_summary(self, baseline_location: str, 
                                      comparison_locations: List[str]) -> Dict[str, Any]:
        """Get a summary for UI display of what will be compared."""
        summary = {
            'baseline': {
                'location': baseline_location,
                'data': self.get_location_data(baseline_location)
            },
            'comparisons': [],
            'constant_parameters': [
                'All financial parameters (PPA price, debt ratio, interest rate)',
                'All grant amounts (Italian, MASEN, Connection, SIMEST, CRI)',
                'Project life and financing structure',
                'Tax parameters and grace periods'
            ],
            'variable_parameters': [
                'Solar production (based on irradiation)',
                'CAPEX (location cost adjustments)',
                'OPEX (regional O&M costs)',
                'Land lease costs',
                'Grid connection costs',
                'Equipment degradation rates'
            ]
        }
        
        for location in comparison_locations:
            if location != baseline_location:
                location_data = self.get_location_data(location)
                summary['comparisons'].append({
                    'location': location,
                    'data': location_data
                })
        
        return summary
